{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/hostelmeet/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached: MongooseCache = global.mongoose || { conn: null, promise: null };\n\nif (!global.mongoose) {\n  global.mongoose = cached;\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAWA,IAAI,SAAwB,OAAO,QAAQ,IAAI;IAAE,MAAM;IAAM,SAAS;AAAK;AAE3E,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;AACpB;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa;IACjD;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/hostelmeet/src/models/Hostel.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IHostel extends Document {\n  name: string;\n  headline: string;\n  address: string;\n  location: {\n    latitude: number;\n    longitude: number;\n  };\n  nearbyLandmarks: string[];\n  nearestCollege: string;\n  pricing: {\n    fourSeater: {\n      ac: number;\n      nonAc: number;\n    };\n    threeSeater: {\n      ac: number;\n      nonAc: number;\n    };\n    twoSeater: {\n      ac: number;\n      nonAc: number;\n    };\n    oneSeater: {\n      ac: number;\n      nonAc: number;\n    };\n  };\n  amenities: string[];\n  travelFacility: string[];\n  foodMenu: {\n    breakfast: string[];\n    lunch: string[];\n    dinner: string[];\n  };\n  lunchProvidedInCollege: boolean;\n  cctv: boolean;\n  securityGuard: boolean;\n  timeRestrictions: {\n    hasRestrictions: boolean;\n    details: string;\n  };\n  careTaker: {\n    name: string;\n    contact: string;\n  };\n  photos: {\n    fourSeater: {\n      ac: string[];\n      nonAc: string[];\n    };\n    threeSeater: {\n      ac: string[];\n      nonAc: string[];\n    };\n    twoSeater: {\n      ac: string[];\n      nonAc: string[];\n    };\n    oneSeater: {\n      ac: string[];\n      nonAc: string[];\n    };\n    common: string[];\n  };\n  gym: boolean;\n  indoorSports: string[];\n  festsAndEvents: {\n    hasEvents: boolean;\n    details: string[];\n  };\n  roomSizes: {\n    fourSeater: string;\n    threeSeater: string;\n    twoSeater: string;\n    oneSeater: string;\n  };\n  owner: {\n    name: string;\n    contact: string;\n    email: string;\n  };\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst HostelSchema = new Schema<IHostel>({\n  name: { type: String, required: true },\n  headline: { type: String, required: true },\n  address: { type: String, required: true },\n  location: {\n    latitude: { type: Number, required: true },\n    longitude: { type: Number, required: true }\n  },\n  nearbyLandmarks: [{ type: String }],\n  nearestCollege: { type: String, required: true },\n  pricing: {\n    fourSeater: {\n      ac: { type: Number, required: true },\n      nonAc: { type: Number, required: true }\n    },\n    threeSeater: {\n      ac: { type: Number, required: true },\n      nonAc: { type: Number, required: true }\n    },\n    twoSeater: {\n      ac: { type: Number, required: true },\n      nonAc: { type: Number, required: true }\n    },\n    oneSeater: {\n      ac: { type: Number, required: true },\n      nonAc: { type: Number, required: true }\n    }\n  },\n  amenities: [{ type: String }],\n  travelFacility: [{ type: String }],\n  foodMenu: {\n    breakfast: [{ type: String }],\n    lunch: [{ type: String }],\n    dinner: [{ type: String }]\n  },\n  lunchProvidedInCollege: { type: Boolean, default: false },\n  cctv: { type: Boolean, default: false },\n  securityGuard: { type: Boolean, default: false },\n  timeRestrictions: {\n    hasRestrictions: { type: Boolean, default: false },\n    details: { type: String, default: '' }\n  },\n  careTaker: {\n    name: { type: String, required: true },\n    contact: { type: String, required: true }\n  },\n  photos: {\n    fourSeater: {\n      ac: [{ type: String }],\n      nonAc: [{ type: String }]\n    },\n    threeSeater: {\n      ac: [{ type: String }],\n      nonAc: [{ type: String }]\n    },\n    twoSeater: {\n      ac: [{ type: String }],\n      nonAc: [{ type: String }]\n    },\n    oneSeater: {\n      ac: [{ type: String }],\n      nonAc: [{ type: String }]\n    },\n    common: [{ type: String }]\n  },\n  gym: { type: Boolean, default: false },\n  indoorSports: [{ type: String }],\n  festsAndEvents: {\n    hasEvents: { type: Boolean, default: false },\n    details: [{ type: String }]\n  },\n  roomSizes: {\n    fourSeater: { type: String, required: true },\n    threeSeater: { type: String, required: true },\n    twoSeater: { type: String, required: true },\n    oneSeater: { type: String, required: true }\n  },\n  owner: {\n    name: { type: String, required: true },\n    contact: { type: String, required: true },\n    email: { type: String, required: true }\n  }\n}, {\n  timestamps: true\n});\n\n// Create indexes for better search performance\nHostelSchema.index({ name: 'text', headline: 'text', address: 'text' });\nHostelSchema.index({ 'location.latitude': 1, 'location.longitude': 1 });\nHostelSchema.index({ 'pricing.fourSeater.ac': 1 });\nHostelSchema.index({ 'pricing.fourSeater.nonAc': 1 });\n\nexport default mongoose.models.Hostel || mongoose.model<IHostel>('Hostel', HostelSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAwFA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAU;IACvC,MAAM;QAAE,MAAM;QAAQ,UAAU;IAAK;IACrC,UAAU;QAAE,MAAM;QAAQ,UAAU;IAAK;IACzC,SAAS;QAAE,MAAM;QAAQ,UAAU;IAAK;IACxC,UAAU;QACR,UAAU;YAAE,MAAM;YAAQ,UAAU;QAAK;QACzC,WAAW;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC5C;IACA,iBAAiB;QAAC;YAAE,MAAM;QAAO;KAAE;IACnC,gBAAgB;QAAE,MAAM;QAAQ,UAAU;IAAK;IAC/C,SAAS;QACP,YAAY;YACV,IAAI;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACnC,OAAO;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACxC;QACA,aAAa;YACX,IAAI;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACnC,OAAO;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACxC;QACA,WAAW;YACT,IAAI;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACnC,OAAO;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACxC;QACA,WAAW;YACT,IAAI;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACnC,OAAO;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACxC;IACF;IACA,WAAW;QAAC;YAAE,MAAM;QAAO;KAAE;IAC7B,gBAAgB;QAAC;YAAE,MAAM;QAAO;KAAE;IAClC,UAAU;QACR,WAAW;YAAC;gBAAE,MAAM;YAAO;SAAE;QAC7B,OAAO;YAAC;gBAAE,MAAM;YAAO;SAAE;QACzB,QAAQ;YAAC;gBAAE,MAAM;YAAO;SAAE;IAC5B;IACA,wBAAwB;QAAE,MAAM;QAAS,SAAS;IAAM;IACxD,MAAM;QAAE,MAAM;QAAS,SAAS;IAAM;IACtC,eAAe;QAAE,MAAM;QAAS,SAAS;IAAM;IAC/C,kBAAkB;QAChB,iBAAiB;YAAE,MAAM;YAAS,SAAS;QAAM;QACjD,SAAS;YAAE,MAAM;YAAQ,SAAS;QAAG;IACvC;IACA,WAAW;QACT,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,SAAS;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC1C;IACA,QAAQ;QACN,YAAY;YACV,IAAI;gBAAC;oBAAE,MAAM;gBAAO;aAAE;YACtB,OAAO;gBAAC;oBAAE,MAAM;gBAAO;aAAE;QAC3B;QACA,aAAa;YACX,IAAI;gBAAC;oBAAE,MAAM;gBAAO;aAAE;YACtB,OAAO;gBAAC;oBAAE,MAAM;gBAAO;aAAE;QAC3B;QACA,WAAW;YACT,IAAI;gBAAC;oBAAE,MAAM;gBAAO;aAAE;YACtB,OAAO;gBAAC;oBAAE,MAAM;gBAAO;aAAE;QAC3B;QACA,WAAW;YACT,IAAI;gBAAC;oBAAE,MAAM;gBAAO;aAAE;YACtB,OAAO;gBAAC;oBAAE,MAAM;gBAAO;aAAE;QAC3B;QACA,QAAQ;YAAC;gBAAE,MAAM;YAAO;SAAE;IAC5B;IACA,KAAK;QAAE,MAAM;QAAS,SAAS;IAAM;IACrC,cAAc;QAAC;YAAE,MAAM;QAAO;KAAE;IAChC,gBAAgB;QACd,WAAW;YAAE,MAAM;YAAS,SAAS;QAAM;QAC3C,SAAS;YAAC;gBAAE,MAAM;YAAO;SAAE;IAC7B;IACA,WAAW;QACT,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC3C,aAAa;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC5C,WAAW;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC1C,WAAW;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC5C;IACA,OAAO;QACL,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,SAAS;YAAE,MAAM;YAAQ,UAAU;QAAK;QACxC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;AACF,GAAG;IACD,YAAY;AACd;AAEA,+CAA+C;AAC/C,aAAa,KAAK,CAAC;IAAE,MAAM;IAAQ,UAAU;IAAQ,SAAS;AAAO;AACrE,aAAa,KAAK,CAAC;IAAE,qBAAqB;IAAG,sBAAsB;AAAE;AACrE,aAAa,KAAK,CAAC;IAAE,yBAAyB;AAAE;AAChD,aAAa,KAAK,CAAC;IAAE,4BAA4B;AAAE;uCAEpC,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/hostelmeet/src/lib/mockData.ts"], "sourcesContent": ["import { IHostel } from '@/models/Hostel';\n\nexport const mockHostels: IHostel[] = [\n  {\n    _id: '1',\n    name: \"Green Valley Hostel\",\n    headline: \"Comfortable stay with modern amenities near IIT Delhi\",\n    address: \"Sector 15, Dwarka, New Delhi, 110075\",\n    location: {\n      latitude: 28.5921,\n      longitude: 77.0460\n    },\n    nearbyLandmarks: [\"Metro Station\", \"Shopping Mall\", \"Hospital\", \"Bank\"],\n    nearestCollege: \"IIT Delhi\",\n    pricing: {\n      fourSeater: { ac: 8000, nonAc: 6000 },\n      threeSeater: { ac: 10000, nonAc: 8000 },\n      twoSeater: { ac: 12000, nonAc: 10000 },\n      oneSeater: { ac: 15000, nonAc: 12000 }\n    },\n    amenities: [\"WiFi\", \"Laundry\", \"Mess\", \"Study Room\", \"Recreation Room\", \"Parking\"],\n    travelFacility: [\"Bus Service\", \"Metro Connectivity\", \"Auto Stand\"],\n    foodMenu: {\n      breakfast: [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Bread Butter\", \"Tea/Coffee\"],\n      lunch: [\"<PERSON> Rice\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>ad\"],\n      dinner: [\"<PERSON>\", \"<PERSON>\", \"<PERSON>ot<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Sweet\"]\n    },\n    lunchProvidedInCollege: true,\n    cctv: true,\n    securityGuard: true,\n    timeRestrictions: {\n      hasRestrictions: true,\n      details: \"Entry allowed till 11 PM\"\n    },\n    careTaker: {\n      name: \"Rajesh Kumar\",\n      contact: \"+91-9876543210\"\n    },\n    photos: {\n      fourSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop\",\n          \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\",\n          \"https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\",\n          \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\"\n        ]\n      },\n      threeSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop\",\n          \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\"\n        ]\n      },\n      twoSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\",\n          \"https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\"\n        ]\n      },\n      oneSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\"\n        ]\n      },\n      common: [\n        \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop\",\n        \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\",\n        \"https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop\"\n      ]\n    },\n    gym: true,\n    indoorSports: [\"Table Tennis\", \"Carrom\", \"Chess\"],\n    festsAndEvents: {\n      hasEvents: true,\n      details: [\"Annual Day\", \"Sports Day\", \"Cultural Events\"]\n    },\n    roomSizes: {\n      fourSeater: \"12x10 feet\",\n      threeSeater: \"12x12 feet\",\n      twoSeater: \"10x12 feet\",\n      oneSeater: \"8x10 feet\"\n    },\n    owner: {\n      name: \"Suresh Sharma\",\n      contact: \"+91-9876543211\",\n      email: \"<EMAIL>\"\n    },\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15')\n  },\n  {\n    _id: '2',\n    name: \"Blue Sky Residency\",\n    headline: \"Premium hostel with excellent facilities near DU North Campus\",\n    address: \"Kamla Nagar, Delhi, 110007\",\n    location: {\n      latitude: 28.6850,\n      longitude: 77.2090\n    },\n    nearbyLandmarks: [\"Metro Station\", \"Market\", \"Library\", \"Cafe\"],\n    nearestCollege: \"Delhi University North Campus\",\n    pricing: {\n      fourSeater: { ac: 9000, nonAc: 7000 },\n      threeSeater: { ac: 11000, nonAc: 9000 },\n      twoSeater: { ac: 13000, nonAc: 11000 },\n      oneSeater: { ac: 16000, nonAc: 13000 }\n    },\n    amenities: [\"WiFi\", \"Laundry\", \"Mess\", \"Study Room\", \"Library\", \"Parking\", \"Generator\"],\n    travelFacility: [\"Bus Service\", \"Metro Connectivity\", \"Cycle Stand\"],\n    foodMenu: {\n      breakfast: [\"Aloo Paratha\", \"Chole\", \"Tea/Coffee\", \"Fruits\"],\n      lunch: [\"Rajma Rice\", \"Roti\", \"Sabzi\", \"Curd\"],\n      dinner: [\"Biryani\", \"Dal\", \"Roti\", \"Raita\", \"Ice Cream\"]\n    },\n    lunchProvidedInCollege: false,\n    cctv: true,\n    securityGuard: true,\n    timeRestrictions: {\n      hasRestrictions: true,\n      details: \"Entry allowed till 10:30 PM\"\n    },\n    careTaker: {\n      name: \"Priya Singh\",\n      contact: \"+91-9876543212\"\n    },\n    photos: {\n      fourSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\",\n          \"https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\"\n        ]\n      },\n      threeSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\"\n        ]\n      },\n      twoSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\"\n        ]\n      },\n      oneSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\"\n        ]\n      },\n      common: [\n        \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\",\n        \"https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop\"\n      ]\n    },\n    gym: false,\n    indoorSports: [\"Badminton\", \"Table Tennis\"],\n    festsAndEvents: {\n      hasEvents: true,\n      details: [\"Diwali Celebration\", \"Holi Festival\", \"New Year Party\"]\n    },\n    roomSizes: {\n      fourSeater: \"14x12 feet\",\n      threeSeater: \"12x14 feet\",\n      twoSeater: \"10x14 feet\",\n      oneSeater: \"8x12 feet\"\n    },\n    owner: {\n      name: \"Amit Gupta\",\n      contact: \"+91-9876543213\",\n      email: \"<EMAIL>\"\n    },\n    createdAt: new Date('2024-01-20'),\n    updatedAt: new Date('2024-01-20')\n  },\n  {\n    _id: '3',\n    name: \"Sunrise Hostel\",\n    headline: \"Budget-friendly accommodation with all basic amenities\",\n    address: \"Lajpat Nagar, New Delhi, 110024\",\n    location: {\n      latitude: 28.5665,\n      longitude: 77.2431\n    },\n    nearbyLandmarks: [\"Metro Station\", \"Market\", \"Hospital\"],\n    nearestCollege: \"Jamia Millia Islamia\",\n    pricing: {\n      fourSeater: { ac: 7000, nonAc: 5000 },\n      threeSeater: { ac: 8500, nonAc: 6500 },\n      twoSeater: { ac: 10000, nonAc: 8000 },\n      oneSeater: { ac: 12000, nonAc: 9500 }\n    },\n    amenities: [\"WiFi\", \"Laundry\", \"Mess\", \"Study Room\", \"Parking\"],\n    travelFacility: [\"Bus Service\", \"Metro Connectivity\"],\n    foodMenu: {\n      breakfast: [\"Paratha\", \"Tea\", \"Fruits\"],\n      lunch: [\"Dal Rice\", \"Roti\", \"Sabzi\"],\n      dinner: [\"Rice\", \"Dal\", \"Roti\", \"Sabzi\"]\n    },\n    lunchProvidedInCollege: false,\n    cctv: true,\n    securityGuard: false,\n    timeRestrictions: {\n      hasRestrictions: true,\n      details: \"Entry allowed till 11:30 PM\"\n    },\n    careTaker: {\n      name: \"Mohan Lal\",\n      contact: \"+91-9876543214\"\n    },\n    photos: {\n      fourSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\",\n          \"https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop\"\n        ]\n      },\n      threeSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\"\n        ]\n      },\n      twoSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\"\n        ]\n      },\n      oneSeater: {\n        ac: [\n          \"https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop\"\n        ],\n        nonAc: [\n          \"https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop\"\n        ]\n      },\n      common: [\n        \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop\"\n      ]\n    },\n    gym: false,\n    indoorSports: [\"Carrom\"],\n    festsAndEvents: {\n      hasEvents: false,\n      details: []\n    },\n    roomSizes: {\n      fourSeater: \"10x10 feet\",\n      threeSeater: \"10x12 feet\",\n      twoSeater: \"8x10 feet\",\n      oneSeater: \"8x8 feet\"\n    },\n    owner: {\n      name: \"Ramesh Gupta\",\n      contact: \"+91-9876543215\",\n      email: \"<EMAIL>\"\n    },\n    createdAt: new Date('2024-01-25'),\n    updatedAt: new Date('2024-01-25')\n  }\n] as IHostel[];\n"], "names": [], "mappings": ";;;AAEO,MAAM,cAAyB;IACpC;QACE,KAAK;QACL,MAAM;QACN,UAAU;QACV,SAAS;QACT,UAAU;YACR,UAAU;YACV,WAAW;QACb;QACA,iBAAiB;YAAC;YAAiB;YAAiB;YAAY;SAAO;QACvE,gBAAgB;QAChB,SAAS;YACP,YAAY;gBAAE,IAAI;gBAAM,OAAO;YAAK;YACpC,aAAa;gBAAE,IAAI;gBAAO,OAAO;YAAK;YACtC,WAAW;gBAAE,IAAI;gBAAO,OAAO;YAAM;YACrC,WAAW;gBAAE,IAAI;gBAAO,OAAO;YAAM;QACvC;QACA,WAAW;YAAC;YAAQ;YAAW;YAAQ;YAAc;YAAmB;SAAU;QAClF,gBAAgB;YAAC;YAAe;YAAsB;SAAa;QACnE,UAAU;YACR,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAgB;aAAa;YACzD,OAAO;gBAAC;gBAAY;gBAAQ;gBAAS;aAAQ;YAC7C,QAAQ;gBAAC;gBAAQ;gBAAO;gBAAQ;gBAAS;aAAQ;QACnD;QACA,wBAAwB;QACxB,MAAM;QACN,eAAe;QACf,kBAAkB;YAChB,iBAAiB;YACjB,SAAS;QACX;QACA,WAAW;YACT,MAAM;YACN,SAAS;QACX;QACA,QAAQ;YACN,YAAY;gBACV,IAAI;oBACF;oBACA;oBACA;iBACD;gBACD,OAAO;oBACL;oBACA;iBACD;YACH;YACA,aAAa;gBACX,IAAI;oBACF;oBACA;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,WAAW;gBACT,IAAI;oBACF;oBACA;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,WAAW;gBACT,IAAI;oBACF;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA,KAAK;QACL,cAAc;YAAC;YAAgB;YAAU;SAAQ;QACjD,gBAAgB;YACd,WAAW;YACX,SAAS;gBAAC;gBAAc;gBAAc;aAAkB;QAC1D;QACA,WAAW;YACT,YAAY;YACZ,aAAa;YACb,WAAW;YACX,WAAW;QACb;QACA,OAAO;YACL,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,KAAK;QACL,MAAM;QACN,UAAU;QACV,SAAS;QACT,UAAU;YACR,UAAU;YACV,WAAW;QACb;QACA,iBAAiB;YAAC;YAAiB;YAAU;YAAW;SAAO;QAC/D,gBAAgB;QAChB,SAAS;YACP,YAAY;gBAAE,IAAI;gBAAM,OAAO;YAAK;YACpC,aAAa;gBAAE,IAAI;gBAAO,OAAO;YAAK;YACtC,WAAW;gBAAE,IAAI;gBAAO,OAAO;YAAM;YACrC,WAAW;gBAAE,IAAI;gBAAO,OAAO;YAAM;QACvC;QACA,WAAW;YAAC;YAAQ;YAAW;YAAQ;YAAc;YAAW;YAAW;SAAY;QACvF,gBAAgB;YAAC;YAAe;YAAsB;SAAc;QACpE,UAAU;YACR,WAAW;gBAAC;gBAAgB;gBAAS;gBAAc;aAAS;YAC5D,OAAO;gBAAC;gBAAc;gBAAQ;gBAAS;aAAO;YAC9C,QAAQ;gBAAC;gBAAW;gBAAO;gBAAQ;gBAAS;aAAY;QAC1D;QACA,wBAAwB;QACxB,MAAM;QACN,eAAe;QACf,kBAAkB;YAChB,iBAAiB;YACjB,SAAS;QACX;QACA,WAAW;YACT,MAAM;YACN,SAAS;QACX;QACA,QAAQ;YACN,YAAY;gBACV,IAAI;oBACF;oBACA;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,aAAa;gBACX,IAAI;oBACF;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,WAAW;gBACT,IAAI;oBACF;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,WAAW;gBACT,IAAI;oBACF;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,QAAQ;gBACN;gBACA;aACD;QACH;QACA,KAAK;QACL,cAAc;YAAC;YAAa;SAAe;QAC3C,gBAAgB;YACd,WAAW;YACX,SAAS;gBAAC;gBAAsB;gBAAiB;aAAiB;QACpE;QACA,WAAW;YACT,YAAY;YACZ,aAAa;YACb,WAAW;YACX,WAAW;QACb;QACA,OAAO;YACL,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,KAAK;QACL,MAAM;QACN,UAAU;QACV,SAAS;QACT,UAAU;YACR,UAAU;YACV,WAAW;QACb;QACA,iBAAiB;YAAC;YAAiB;YAAU;SAAW;QACxD,gBAAgB;QAChB,SAAS;YACP,YAAY;gBAAE,IAAI;gBAAM,OAAO;YAAK;YACpC,aAAa;gBAAE,IAAI;gBAAM,OAAO;YAAK;YACrC,WAAW;gBAAE,IAAI;gBAAO,OAAO;YAAK;YACpC,WAAW;gBAAE,IAAI;gBAAO,OAAO;YAAK;QACtC;QACA,WAAW;YAAC;YAAQ;YAAW;YAAQ;YAAc;SAAU;QAC/D,gBAAgB;YAAC;YAAe;SAAqB;QACrD,UAAU;YACR,WAAW;gBAAC;gBAAW;gBAAO;aAAS;YACvC,OAAO;gBAAC;gBAAY;gBAAQ;aAAQ;YACpC,QAAQ;gBAAC;gBAAQ;gBAAO;gBAAQ;aAAQ;QAC1C;QACA,wBAAwB;QACxB,MAAM;QACN,eAAe;QACf,kBAAkB;YAChB,iBAAiB;YACjB,SAAS;QACX;QACA,WAAW;YACT,MAAM;YACN,SAAS;QACX;QACA,QAAQ;YACN,YAAY;gBACV,IAAI;oBACF;iBACD;gBACD,OAAO;oBACL;oBACA;iBACD;YACH;YACA,aAAa;gBACX,IAAI;oBACF;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,WAAW;gBACT,IAAI;oBACF;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,WAAW;gBACT,IAAI;oBACF;iBACD;gBACD,OAAO;oBACL;iBACD;YACH;YACA,QAAQ;gBACN;aACD;QACH;QACA,KAAK;QACL,cAAc;YAAC;SAAS;QACxB,gBAAgB;YACd,WAAW;YACX,SAAS,EAAE;QACb;QACA,WAAW;YACT,YAAY;YACZ,aAAa;YACb,WAAW;YACX,WAAW;QACb;QACA,OAAO;YACL,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD", "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/hostelmeet/src/app/api/hostels/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Hostel from '@/models/Hostel';\nimport { mockHostels } from '@/lib/mockData';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '12');\n    const search = searchParams.get('search') || '';\n\n    // Try to connect to MongoDB, fallback to mock data if it fails\n    let hostels;\n    let total;\n\n    try {\n      await connectDB();\n\n      const seater = searchParams.get('seater') || '';\n      const ac = searchParams.get('ac') || '';\n      const minPrice = searchParams.get('minPrice');\n      const maxPrice = searchParams.get('maxPrice');\n      const amenities = searchParams.get('amenities')?.split(',') || [];\n      const sortBy = searchParams.get('sortBy') || 'name';\n      const sortOrder = searchParams.get('sortOrder') || 'asc';\n\n      // Build query\n      const query: any = {};\n\n      // Search by name, headline, or address\n      if (search) {\n        query.$or = [\n          { name: { $regex: search, $options: 'i' } },\n          { headline: { $regex: search, $options: 'i' } },\n          { address: { $regex: search, $options: 'i' } },\n          { nearestCollege: { $regex: search, $options: 'i' } }\n        ];\n      }\n\n      // Filter by amenities\n      if (amenities.length > 0) {\n        query.amenities = { $in: amenities };\n      }\n\n      // Price filtering (complex due to nested structure)\n      if (minPrice || maxPrice) {\n        const priceConditions: any[] = [];\n        const seaterTypes = ['fourSeater', 'threeSeater', 'twoSeater', 'oneSeater'];\n        const acTypes = ['ac', 'nonAc'];\n\n        seaterTypes.forEach(seaterType => {\n          acTypes.forEach(acType => {\n            const condition: any = {};\n            if (minPrice) condition[`pricing.${seaterType}.${acType}`] = { $gte: parseInt(minPrice) };\n            if (maxPrice) {\n              if (condition[`pricing.${seaterType}.${acType}`]) {\n                condition[`pricing.${seaterType}.${acType}`].$lte = parseInt(maxPrice);\n              } else {\n                condition[`pricing.${seaterType}.${acType}`] = { $lte: parseInt(maxPrice) };\n              }\n            }\n            priceConditions.push(condition);\n          });\n        });\n\n        if (priceConditions.length > 0) {\n          query.$or = query.$or ? [...query.$or, ...priceConditions] : priceConditions;\n        }\n      }\n\n      // Build sort object\n      const sort: any = {};\n      if (sortBy === 'price') {\n        // Sort by minimum price\n        sort['pricing.fourSeater.nonAc'] = sortOrder === 'asc' ? 1 : -1;\n      } else {\n        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;\n      }\n\n      const skip = (page - 1) * limit;\n\n      const [dbHostels, dbTotal] = await Promise.all([\n        Hostel.find(query)\n          .sort(sort)\n          .skip(skip)\n          .limit(limit)\n          .lean(),\n        Hostel.countDocuments(query)\n      ]);\n\n      hostels = dbHostels;\n      total = dbTotal;\n\n    } catch (dbError) {\n      console.log('MongoDB connection failed, using mock data');\n\n      // Use mock data with filtering\n      let filteredHostels = [...mockHostels];\n\n      // Apply search filter\n      if (search) {\n        filteredHostels = filteredHostels.filter(hostel =>\n          hostel.name.toLowerCase().includes(search.toLowerCase()) ||\n          hostel.headline.toLowerCase().includes(search.toLowerCase()) ||\n          hostel.address.toLowerCase().includes(search.toLowerCase()) ||\n          hostel.nearestCollege.toLowerCase().includes(search.toLowerCase())\n        );\n      }\n\n      total = filteredHostels.length;\n      const skip = (page - 1) * limit;\n      hostels = filteredHostels.slice(skip, skip + limit);\n    }\n\n    return NextResponse.json({\n      hostels,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    });\n\n  } catch (error) {\n    console.error('Error fetching hostels:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch hostels' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    await connectDB();\n\n    const body = await request.json();\n    const hostel = new Hostel(body);\n    await hostel.save();\n\n    return NextResponse.json(hostel, { status: 201 });\n\n  } catch (error) {\n    console.error('Error creating hostel:', error);\n    return NextResponse.json(\n      { error: 'Failed to create hostel' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,+DAA+D;QAC/D,IAAI;QACJ,IAAI;QAEJ,IAAI;YACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YAEd,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;YAC7C,MAAM,KAAK,aAAa,GAAG,CAAC,SAAS;YACrC,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,MAAM,YAAY,aAAa,GAAG,CAAC,cAAc,MAAM,QAAQ,EAAE;YACjE,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;YAC7C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;YAEnD,cAAc;YACd,MAAM,QAAa,CAAC;YAEpB,uCAAuC;YACvC,IAAI,QAAQ;gBACV,MAAM,GAAG,GAAG;oBACV;wBAAE,MAAM;4BAAE,QAAQ;4BAAQ,UAAU;wBAAI;oBAAE;oBAC1C;wBAAE,UAAU;4BAAE,QAAQ;4BAAQ,UAAU;wBAAI;oBAAE;oBAC9C;wBAAE,SAAS;4BAAE,QAAQ;4BAAQ,UAAU;wBAAI;oBAAE;oBAC7C;wBAAE,gBAAgB;4BAAE,QAAQ;4BAAQ,UAAU;wBAAI;oBAAE;iBACrD;YACH;YAEA,sBAAsB;YACtB,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,MAAM,SAAS,GAAG;oBAAE,KAAK;gBAAU;YACrC;YAEA,oDAAoD;YACpD,IAAI,YAAY,UAAU;gBACxB,MAAM,kBAAyB,EAAE;gBACjC,MAAM,cAAc;oBAAC;oBAAc;oBAAe;oBAAa;iBAAY;gBAC3E,MAAM,UAAU;oBAAC;oBAAM;iBAAQ;gBAE/B,YAAY,OAAO,CAAC,CAAA;oBAClB,QAAQ,OAAO,CAAC,CAAA;wBACd,MAAM,YAAiB,CAAC;wBACxB,IAAI,UAAU,SAAS,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,GAAG;4BAAE,MAAM,SAAS;wBAAU;wBACxF,IAAI,UAAU;4BACZ,IAAI,SAAS,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,EAAE;gCAChD,SAAS,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,GAAG,SAAS;4BAC/D,OAAO;gCACL,SAAS,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,GAAG;oCAAE,MAAM,SAAS;gCAAU;4BAC5E;wBACF;wBACA,gBAAgB,IAAI,CAAC;oBACvB;gBACF;gBAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;oBAC9B,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG;2BAAI,MAAM,GAAG;2BAAK;qBAAgB,GAAG;gBAC/D;YACF;YAEA,oBAAoB;YACpB,MAAM,OAAY,CAAC;YACnB,IAAI,WAAW,SAAS;gBACtB,wBAAwB;gBACxB,IAAI,CAAC,2BAA2B,GAAG,cAAc,QAAQ,IAAI,CAAC;YAChE,OAAO;gBACL,IAAI,CAAC,OAAO,GAAG,cAAc,QAAQ,IAAI,CAAC;YAC5C;YAEA,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YAE1B,MAAM,CAAC,WAAW,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7C,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,OACT,IAAI,CAAC,MACL,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;gBACP,yHAAA,CAAA,UAAM,CAAC,cAAc,CAAC;aACvB;YAED,UAAU;YACV,QAAQ;QAEV,EAAE,OAAO,SAAS;YAChB,QAAQ,GAAG,CAAC;YAEZ,+BAA+B;YAC/B,IAAI,kBAAkB;mBAAI,wHAAA,CAAA,cAAW;aAAC;YAEtC,sBAAsB;YACtB,IAAI,QAAQ;gBACV,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,SACvC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW,OACrD,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW,OACzD,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW,OACxD,OAAO,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW;YAEnE;YAEA,QAAQ,gBAAgB,MAAM;YAC9B,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YAC1B,UAAU,gBAAgB,KAAK,CAAC,MAAM,OAAO;QAC/C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;QAC1B,MAAM,OAAO,IAAI;QAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;YAAE,QAAQ;QAAI;IAEjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}