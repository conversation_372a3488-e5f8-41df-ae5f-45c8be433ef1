module.exports = {

"[project]/.next-internal/server/app/api/hostels/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
let cached = global.mongoose || {
    conn: null,
    promise: null
};
if (!global.mongoose) {
    global.mongoose = cached;
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts);
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Hostel.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const HostelSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: true
    },
    headline: {
        type: String,
        required: true
    },
    address: {
        type: String,
        required: true
    },
    location: {
        latitude: {
            type: Number,
            required: true
        },
        longitude: {
            type: Number,
            required: true
        }
    },
    nearbyLandmarks: [
        {
            type: String
        }
    ],
    nearestCollege: {
        type: String,
        required: true
    },
    pricing: {
        fourSeater: {
            ac: {
                type: Number,
                required: true
            },
            nonAc: {
                type: Number,
                required: true
            }
        },
        threeSeater: {
            ac: {
                type: Number,
                required: true
            },
            nonAc: {
                type: Number,
                required: true
            }
        },
        twoSeater: {
            ac: {
                type: Number,
                required: true
            },
            nonAc: {
                type: Number,
                required: true
            }
        },
        oneSeater: {
            ac: {
                type: Number,
                required: true
            },
            nonAc: {
                type: Number,
                required: true
            }
        }
    },
    amenities: [
        {
            type: String
        }
    ],
    travelFacility: [
        {
            type: String
        }
    ],
    foodMenu: {
        breakfast: [
            {
                type: String
            }
        ],
        lunch: [
            {
                type: String
            }
        ],
        dinner: [
            {
                type: String
            }
        ]
    },
    lunchProvidedInCollege: {
        type: Boolean,
        default: false
    },
    cctv: {
        type: Boolean,
        default: false
    },
    securityGuard: {
        type: Boolean,
        default: false
    },
    timeRestrictions: {
        hasRestrictions: {
            type: Boolean,
            default: false
        },
        details: {
            type: String,
            default: ''
        }
    },
    careTaker: {
        name: {
            type: String,
            required: true
        },
        contact: {
            type: String,
            required: true
        }
    },
    photos: {
        fourSeater: {
            ac: [
                {
                    type: String
                }
            ],
            nonAc: [
                {
                    type: String
                }
            ]
        },
        threeSeater: {
            ac: [
                {
                    type: String
                }
            ],
            nonAc: [
                {
                    type: String
                }
            ]
        },
        twoSeater: {
            ac: [
                {
                    type: String
                }
            ],
            nonAc: [
                {
                    type: String
                }
            ]
        },
        oneSeater: {
            ac: [
                {
                    type: String
                }
            ],
            nonAc: [
                {
                    type: String
                }
            ]
        },
        common: [
            {
                type: String
            }
        ]
    },
    gym: {
        type: Boolean,
        default: false
    },
    indoorSports: [
        {
            type: String
        }
    ],
    festsAndEvents: {
        hasEvents: {
            type: Boolean,
            default: false
        },
        details: [
            {
                type: String
            }
        ]
    },
    roomSizes: {
        fourSeater: {
            type: String,
            required: true
        },
        threeSeater: {
            type: String,
            required: true
        },
        twoSeater: {
            type: String,
            required: true
        },
        oneSeater: {
            type: String,
            required: true
        }
    },
    owner: {
        name: {
            type: String,
            required: true
        },
        contact: {
            type: String,
            required: true
        },
        email: {
            type: String,
            required: true
        }
    }
}, {
    timestamps: true
});
// Create indexes for better search performance
HostelSchema.index({
    name: 'text',
    headline: 'text',
    address: 'text'
});
HostelSchema.index({
    'location.latitude': 1,
    'location.longitude': 1
});
HostelSchema.index({
    'pricing.fourSeater.ac': 1
});
HostelSchema.index({
    'pricing.fourSeater.nonAc': 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Hostel || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Hostel', HostelSchema);
}}),
"[project]/src/lib/mockData.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mockHostels": (()=>mockHostels)
});
const mockHostels = [
    {
        _id: '1',
        name: "Green Valley Hostel",
        headline: "Comfortable stay with modern amenities near IIT Delhi",
        address: "Sector 15, Dwarka, New Delhi, 110075",
        location: {
            latitude: 28.5921,
            longitude: 77.0460
        },
        nearbyLandmarks: [
            "Metro Station",
            "Shopping Mall",
            "Hospital",
            "Bank"
        ],
        nearestCollege: "IIT Delhi",
        pricing: {
            fourSeater: {
                ac: 8000,
                nonAc: 6000
            },
            threeSeater: {
                ac: 10000,
                nonAc: 8000
            },
            twoSeater: {
                ac: 12000,
                nonAc: 10000
            },
            oneSeater: {
                ac: 15000,
                nonAc: 12000
            }
        },
        amenities: [
            "WiFi",
            "Laundry",
            "Mess",
            "Study Room",
            "Recreation Room",
            "Parking"
        ],
        travelFacility: [
            "Bus Service",
            "Metro Connectivity",
            "Auto Stand"
        ],
        foodMenu: {
            breakfast: [
                "Poha",
                "Upma",
                "Bread Butter",
                "Tea/Coffee"
            ],
            lunch: [
                "Dal Rice",
                "Roti",
                "Sabzi",
                "Salad"
            ],
            dinner: [
                "Rice",
                "Dal",
                "Roti",
                "Sabzi",
                "Sweet"
            ]
        },
        lunchProvidedInCollege: true,
        cctv: true,
        securityGuard: true,
        timeRestrictions: {
            hasRestrictions: true,
            details: "Entry allowed till 11 PM"
        },
        careTaker: {
            name: "Rajesh Kumar",
            contact: "+91-9876543210"
        },
        photos: {
            fourSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop",
                    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop",
                    "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop",
                    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop"
                ]
            },
            threeSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop",
                    "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop"
                ]
            },
            twoSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop",
                    "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop"
                ]
            },
            oneSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop"
                ]
            },
            common: [
                "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop",
                "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop",
                "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop"
            ]
        },
        gym: true,
        indoorSports: [
            "Table Tennis",
            "Carrom",
            "Chess"
        ],
        festsAndEvents: {
            hasEvents: true,
            details: [
                "Annual Day",
                "Sports Day",
                "Cultural Events"
            ]
        },
        roomSizes: {
            fourSeater: "12x10 feet",
            threeSeater: "12x12 feet",
            twoSeater: "10x12 feet",
            oneSeater: "8x10 feet"
        },
        owner: {
            name: "Suresh Sharma",
            contact: "+91-9876543211",
            email: "<EMAIL>"
        },
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15')
    },
    {
        _id: '2',
        name: "Blue Sky Residency",
        headline: "Premium hostel with excellent facilities near DU North Campus",
        address: "Kamla Nagar, Delhi, 110007",
        location: {
            latitude: 28.6850,
            longitude: 77.2090
        },
        nearbyLandmarks: [
            "Metro Station",
            "Market",
            "Library",
            "Cafe"
        ],
        nearestCollege: "Delhi University North Campus",
        pricing: {
            fourSeater: {
                ac: 9000,
                nonAc: 7000
            },
            threeSeater: {
                ac: 11000,
                nonAc: 9000
            },
            twoSeater: {
                ac: 13000,
                nonAc: 11000
            },
            oneSeater: {
                ac: 16000,
                nonAc: 13000
            }
        },
        amenities: [
            "WiFi",
            "Laundry",
            "Mess",
            "Study Room",
            "Library",
            "Parking",
            "Generator"
        ],
        travelFacility: [
            "Bus Service",
            "Metro Connectivity",
            "Cycle Stand"
        ],
        foodMenu: {
            breakfast: [
                "Aloo Paratha",
                "Chole",
                "Tea/Coffee",
                "Fruits"
            ],
            lunch: [
                "Rajma Rice",
                "Roti",
                "Sabzi",
                "Curd"
            ],
            dinner: [
                "Biryani",
                "Dal",
                "Roti",
                "Raita",
                "Ice Cream"
            ]
        },
        lunchProvidedInCollege: false,
        cctv: true,
        securityGuard: true,
        timeRestrictions: {
            hasRestrictions: true,
            details: "Entry allowed till 10:30 PM"
        },
        careTaker: {
            name: "Priya Singh",
            contact: "+91-9876543212"
        },
        photos: {
            fourSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop",
                    "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop"
                ]
            },
            threeSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop"
                ]
            },
            twoSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop"
                ]
            },
            oneSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop"
                ]
            },
            common: [
                "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop",
                "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop"
            ]
        },
        gym: false,
        indoorSports: [
            "Badminton",
            "Table Tennis"
        ],
        festsAndEvents: {
            hasEvents: true,
            details: [
                "Diwali Celebration",
                "Holi Festival",
                "New Year Party"
            ]
        },
        roomSizes: {
            fourSeater: "14x12 feet",
            threeSeater: "12x14 feet",
            twoSeater: "10x14 feet",
            oneSeater: "8x12 feet"
        },
        owner: {
            name: "Amit Gupta",
            contact: "+91-9876543213",
            email: "<EMAIL>"
        },
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-01-20')
    },
    {
        _id: '3',
        name: "Sunrise Hostel",
        headline: "Budget-friendly accommodation with all basic amenities",
        address: "Lajpat Nagar, New Delhi, 110024",
        location: {
            latitude: 28.5665,
            longitude: 77.2431
        },
        nearbyLandmarks: [
            "Metro Station",
            "Market",
            "Hospital"
        ],
        nearestCollege: "Jamia Millia Islamia",
        pricing: {
            fourSeater: {
                ac: 7000,
                nonAc: 5000
            },
            threeSeater: {
                ac: 8500,
                nonAc: 6500
            },
            twoSeater: {
                ac: 10000,
                nonAc: 8000
            },
            oneSeater: {
                ac: 12000,
                nonAc: 9500
            }
        },
        amenities: [
            "WiFi",
            "Laundry",
            "Mess",
            "Study Room",
            "Parking"
        ],
        travelFacility: [
            "Bus Service",
            "Metro Connectivity"
        ],
        foodMenu: {
            breakfast: [
                "Paratha",
                "Tea",
                "Fruits"
            ],
            lunch: [
                "Dal Rice",
                "Roti",
                "Sabzi"
            ],
            dinner: [
                "Rice",
                "Dal",
                "Roti",
                "Sabzi"
            ]
        },
        lunchProvidedInCollege: false,
        cctv: true,
        securityGuard: false,
        timeRestrictions: {
            hasRestrictions: true,
            details: "Entry allowed till 11:30 PM"
        },
        careTaker: {
            name: "Mohan Lal",
            contact: "+91-9876543214"
        },
        photos: {
            fourSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop",
                    "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop"
                ]
            },
            threeSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop"
                ]
            },
            twoSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop"
                ]
            },
            oneSeater: {
                ac: [
                    "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop"
                ],
                nonAc: [
                    "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop"
                ]
            },
            common: [
                "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop"
            ]
        },
        gym: false,
        indoorSports: [
            "Carrom"
        ],
        festsAndEvents: {
            hasEvents: false,
            details: []
        },
        roomSizes: {
            fourSeater: "10x10 feet",
            threeSeater: "10x12 feet",
            twoSeater: "8x10 feet",
            oneSeater: "8x8 feet"
        },
        owner: {
            name: "Ramesh Gupta",
            contact: "+91-9876543215",
            email: "<EMAIL>"
        },
        createdAt: new Date('2024-01-25'),
        updatedAt: new Date('2024-01-25')
    }
];
}}),
"[project]/src/app/api/hostels/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Hostel.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mockData.ts [app-route] (ecmascript)");
;
;
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '12');
        const search = searchParams.get('search') || '';
        // Try to connect to MongoDB, fallback to mock data if it fails
        let hostels;
        let total;
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            const seater = searchParams.get('seater') || '';
            const ac = searchParams.get('ac') || '';
            const minPrice = searchParams.get('minPrice');
            const maxPrice = searchParams.get('maxPrice');
            const amenities = searchParams.get('amenities')?.split(',') || [];
            const sortBy = searchParams.get('sortBy') || 'name';
            const sortOrder = searchParams.get('sortOrder') || 'asc';
            // Build query
            const query = {};
            // Search by name, headline, or address
            if (search) {
                query.$or = [
                    {
                        name: {
                            $regex: search,
                            $options: 'i'
                        }
                    },
                    {
                        headline: {
                            $regex: search,
                            $options: 'i'
                        }
                    },
                    {
                        address: {
                            $regex: search,
                            $options: 'i'
                        }
                    },
                    {
                        nearestCollege: {
                            $regex: search,
                            $options: 'i'
                        }
                    }
                ];
            }
            // Filter by amenities
            if (amenities.length > 0) {
                query.amenities = {
                    $in: amenities
                };
            }
            // Price filtering (complex due to nested structure)
            if (minPrice || maxPrice) {
                const priceConditions = [];
                const seaterTypes = [
                    'fourSeater',
                    'threeSeater',
                    'twoSeater',
                    'oneSeater'
                ];
                const acTypes = [
                    'ac',
                    'nonAc'
                ];
                seaterTypes.forEach((seaterType)=>{
                    acTypes.forEach((acType)=>{
                        const condition = {};
                        if (minPrice) condition[`pricing.${seaterType}.${acType}`] = {
                            $gte: parseInt(minPrice)
                        };
                        if (maxPrice) {
                            if (condition[`pricing.${seaterType}.${acType}`]) {
                                condition[`pricing.${seaterType}.${acType}`].$lte = parseInt(maxPrice);
                            } else {
                                condition[`pricing.${seaterType}.${acType}`] = {
                                    $lte: parseInt(maxPrice)
                                };
                            }
                        }
                        priceConditions.push(condition);
                    });
                });
                if (priceConditions.length > 0) {
                    query.$or = query.$or ? [
                        ...query.$or,
                        ...priceConditions
                    ] : priceConditions;
                }
            }
            // Build sort object
            const sort = {};
            if (sortBy === 'price') {
                // Sort by minimum price
                sort['pricing.fourSeater.nonAc'] = sortOrder === 'asc' ? 1 : -1;
            } else {
                sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            }
            const skip = (page - 1) * limit;
            const [dbHostels, dbTotal] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find(query).sort(sort).skip(skip).limit(limit).lean(),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments(query)
            ]);
            hostels = dbHostels;
            total = dbTotal;
        } catch (dbError) {
            console.log('MongoDB connection failed, using mock data');
            // Use mock data with filtering
            let filteredHostels = [
                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockHostels"]
            ];
            // Apply search filter
            if (search) {
                filteredHostels = filteredHostels.filter((hostel)=>hostel.name.toLowerCase().includes(search.toLowerCase()) || hostel.headline.toLowerCase().includes(search.toLowerCase()) || hostel.address.toLowerCase().includes(search.toLowerCase()) || hostel.nearestCollege.toLowerCase().includes(search.toLowerCase()));
            }
            total = filteredHostels.length;
            const skip = (page - 1) * limit;
            hostels = filteredHostels.slice(skip, skip + limit);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            hostels,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching hostels:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch hostels'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const body = await request.json();
        const hostel = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"](body);
        await hostel.save();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(hostel, {
            status: 201
        });
    } catch (error) {
        console.error('Error creating hostel:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to create hostel'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d0fc9394._.js.map