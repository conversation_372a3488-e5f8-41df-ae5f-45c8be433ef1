{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/hostelmeet/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';\nconst ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';\nconst ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'nk10nikhil';\n\nexport interface AdminUser {\n  email: string;\n  isAdmin: boolean;\n}\n\nexport async function verifyAdminCredentials(email: string, password: string): Promise<boolean> {\n  return email === ADMIN_EMAIL && password === ADMIN_PASSWORD;\n}\n\nexport function generateToken(user: AdminUser): string {\n  return jwt.sign(user, JWT_SECRET, { expiresIn: '24h' });\n}\n\nexport function verifyToken(token: string): AdminUser | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as AdminUser;\n  } catch (error) {\n    return null;\n  }\n}\n\nexport function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12);\n}\n\nexport function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAC/C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AAO9C,eAAe,uBAAuB,KAAa,EAAE,QAAgB;IAC1E,OAAO,UAAU,eAAe,aAAa;AAC/C;AAEO,SAAS,cAAc,IAAe;IAC3C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,MAAM,YAAY;QAAE,WAAW;IAAM;AACvD;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,aAAa,QAAgB;IAC3C,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,SAAS,gBAAgB,QAAgB,EAAE,cAAsB;IACtE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/hostelmeet/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { verifyAdminCredentials, generateToken } from '@/lib/auth';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, password } = await request.json();\n\n    if (!email || !password) {\n      return NextResponse.json(\n        { error: 'Email and password are required' },\n        { status: 400 }\n      );\n    }\n\n    const isValid = await verifyAdminCredentials(email, password);\n\n    if (!isValid) {\n      return NextResponse.json(\n        { error: 'Invalid credentials' },\n        { status: 401 }\n      );\n    }\n\n    const token = generateToken({ email, isAdmin: true });\n\n    const response = NextResponse.json(\n      { message: 'Login successful', user: { email, isAdmin: true } },\n      { status: 200 }\n    );\n\n    // Set HTTP-only cookie\n    response.cookies.set('auth-token', token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'strict',\n      maxAge: 24 * 60 * 60 * 1000, // 24 hours\n    });\n\n    return response;\n\n  } catch (error) {\n    console.error('Login error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9C,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO;QAEpD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE;YAAE;YAAO,SAAS;QAAK;QAEnD,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAChC;YAAE,SAAS;YAAoB,MAAM;gBAAE;gBAAO,SAAS;YAAK;QAAE,GAC9D;YAAE,QAAQ;QAAI;QAGhB,uBAAuB;QACvB,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO;YACxC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,KAAK,KAAK,KAAK;QACzB;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}