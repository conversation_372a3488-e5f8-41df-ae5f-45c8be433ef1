{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/hostelmeet/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached: MongooseCache = global.mongoose || { conn: null, promise: null };\n\nif (!global.mongoose) {\n  global.mongoose = cached;\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAWA,IAAI,SAAwB,OAAO,QAAQ,IAAI;IAAE,MAAM;IAAM,SAAS;AAAK;AAE3E,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;AACpB;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa;IACjD;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/hostelmeet/src/models/Hostel.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IHostel extends Document {\n  name: string;\n  headline: string;\n  address: string;\n  location: {\n    latitude: number;\n    longitude: number;\n  };\n  nearbyLandmarks: string[];\n  nearestCollege: string;\n  pricing: {\n    fourSeater: {\n      ac: number;\n      nonAc: number;\n    };\n    threeSeater: {\n      ac: number;\n      nonAc: number;\n    };\n    twoSeater: {\n      ac: number;\n      nonAc: number;\n    };\n    oneSeater: {\n      ac: number;\n      nonAc: number;\n    };\n  };\n  amenities: string[];\n  travelFacility: string[];\n  foodMenu: {\n    breakfast: string[];\n    lunch: string[];\n    dinner: string[];\n  };\n  lunchProvidedInCollege: boolean;\n  cctv: boolean;\n  securityGuard: boolean;\n  timeRestrictions: {\n    hasRestrictions: boolean;\n    details: string;\n  };\n  careTaker: {\n    name: string;\n    contact: string;\n  };\n  photos: {\n    fourSeater: {\n      ac: string[];\n      nonAc: string[];\n    };\n    threeSeater: {\n      ac: string[];\n      nonAc: string[];\n    };\n    twoSeater: {\n      ac: string[];\n      nonAc: string[];\n    };\n    oneSeater: {\n      ac: string[];\n      nonAc: string[];\n    };\n    common: string[];\n  };\n  gym: boolean;\n  indoorSports: string[];\n  festsAndEvents: {\n    hasEvents: boolean;\n    details: string[];\n  };\n  roomSizes: {\n    fourSeater: string;\n    threeSeater: string;\n    twoSeater: string;\n    oneSeater: string;\n  };\n  owner: {\n    name: string;\n    contact: string;\n    email: string;\n  };\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst HostelSchema = new Schema<IHostel>({\n  name: { type: String, required: true },\n  headline: { type: String, required: true },\n  address: { type: String, required: true },\n  location: {\n    latitude: { type: Number, required: true },\n    longitude: { type: Number, required: true }\n  },\n  nearbyLandmarks: [{ type: String }],\n  nearestCollege: { type: String, required: true },\n  pricing: {\n    fourSeater: {\n      ac: { type: Number, required: true },\n      nonAc: { type: Number, required: true }\n    },\n    threeSeater: {\n      ac: { type: Number, required: true },\n      nonAc: { type: Number, required: true }\n    },\n    twoSeater: {\n      ac: { type: Number, required: true },\n      nonAc: { type: Number, required: true }\n    },\n    oneSeater: {\n      ac: { type: Number, required: true },\n      nonAc: { type: Number, required: true }\n    }\n  },\n  amenities: [{ type: String }],\n  travelFacility: [{ type: String }],\n  foodMenu: {\n    breakfast: [{ type: String }],\n    lunch: [{ type: String }],\n    dinner: [{ type: String }]\n  },\n  lunchProvidedInCollege: { type: Boolean, default: false },\n  cctv: { type: Boolean, default: false },\n  securityGuard: { type: Boolean, default: false },\n  timeRestrictions: {\n    hasRestrictions: { type: Boolean, default: false },\n    details: { type: String, default: '' }\n  },\n  careTaker: {\n    name: { type: String, required: true },\n    contact: { type: String, required: true }\n  },\n  photos: {\n    fourSeater: {\n      ac: [{ type: String }],\n      nonAc: [{ type: String }]\n    },\n    threeSeater: {\n      ac: [{ type: String }],\n      nonAc: [{ type: String }]\n    },\n    twoSeater: {\n      ac: [{ type: String }],\n      nonAc: [{ type: String }]\n    },\n    oneSeater: {\n      ac: [{ type: String }],\n      nonAc: [{ type: String }]\n    },\n    common: [{ type: String }]\n  },\n  gym: { type: Boolean, default: false },\n  indoorSports: [{ type: String }],\n  festsAndEvents: {\n    hasEvents: { type: Boolean, default: false },\n    details: [{ type: String }]\n  },\n  roomSizes: {\n    fourSeater: { type: String, required: true },\n    threeSeater: { type: String, required: true },\n    twoSeater: { type: String, required: true },\n    oneSeater: { type: String, required: true }\n  },\n  owner: {\n    name: { type: String, required: true },\n    contact: { type: String, required: true },\n    email: { type: String, required: true }\n  }\n}, {\n  timestamps: true\n});\n\n// Create indexes for better search performance\nHostelSchema.index({ name: 'text', headline: 'text', address: 'text' });\nHostelSchema.index({ 'location.latitude': 1, 'location.longitude': 1 });\nHostelSchema.index({ 'pricing.fourSeater.ac': 1 });\nHostelSchema.index({ 'pricing.fourSeater.nonAc': 1 });\n\nexport default mongoose.models.Hostel || mongoose.model<IHostel>('Hostel', HostelSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAwFA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAU;IACvC,MAAM;QAAE,MAAM;QAAQ,UAAU;IAAK;IACrC,UAAU;QAAE,MAAM;QAAQ,UAAU;IAAK;IACzC,SAAS;QAAE,MAAM;QAAQ,UAAU;IAAK;IACxC,UAAU;QACR,UAAU;YAAE,MAAM;YAAQ,UAAU;QAAK;QACzC,WAAW;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC5C;IACA,iBAAiB;QAAC;YAAE,MAAM;QAAO;KAAE;IACnC,gBAAgB;QAAE,MAAM;QAAQ,UAAU;IAAK;IAC/C,SAAS;QACP,YAAY;YACV,IAAI;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACnC,OAAO;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACxC;QACA,aAAa;YACX,IAAI;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACnC,OAAO;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACxC;QACA,WAAW;YACT,IAAI;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACnC,OAAO;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACxC;QACA,WAAW;YACT,IAAI;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACnC,OAAO;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACxC;IACF;IACA,WAAW;QAAC;YAAE,MAAM;QAAO;KAAE;IAC7B,gBAAgB;QAAC;YAAE,MAAM;QAAO;KAAE;IAClC,UAAU;QACR,WAAW;YAAC;gBAAE,MAAM;YAAO;SAAE;QAC7B,OAAO;YAAC;gBAAE,MAAM;YAAO;SAAE;QACzB,QAAQ;YAAC;gBAAE,MAAM;YAAO;SAAE;IAC5B;IACA,wBAAwB;QAAE,MAAM;QAAS,SAAS;IAAM;IACxD,MAAM;QAAE,MAAM;QAAS,SAAS;IAAM;IACtC,eAAe;QAAE,MAAM;QAAS,SAAS;IAAM;IAC/C,kBAAkB;QAChB,iBAAiB;YAAE,MAAM;YAAS,SAAS;QAAM;QACjD,SAAS;YAAE,MAAM;YAAQ,SAAS;QAAG;IACvC;IACA,WAAW;QACT,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,SAAS;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC1C;IACA,QAAQ;QACN,YAAY;YACV,IAAI;gBAAC;oBAAE,MAAM;gBAAO;aAAE;YACtB,OAAO;gBAAC;oBAAE,MAAM;gBAAO;aAAE;QAC3B;QACA,aAAa;YACX,IAAI;gBAAC;oBAAE,MAAM;gBAAO;aAAE;YACtB,OAAO;gBAAC;oBAAE,MAAM;gBAAO;aAAE;QAC3B;QACA,WAAW;YACT,IAAI;gBAAC;oBAAE,MAAM;gBAAO;aAAE;YACtB,OAAO;gBAAC;oBAAE,MAAM;gBAAO;aAAE;QAC3B;QACA,WAAW;YACT,IAAI;gBAAC;oBAAE,MAAM;gBAAO;aAAE;YACtB,OAAO;gBAAC;oBAAE,MAAM;gBAAO;aAAE;QAC3B;QACA,QAAQ;YAAC;gBAAE,MAAM;YAAO;SAAE;IAC5B;IACA,KAAK;QAAE,MAAM;QAAS,SAAS;IAAM;IACrC,cAAc;QAAC;YAAE,MAAM;QAAO;KAAE;IAChC,gBAAgB;QACd,WAAW;YAAE,MAAM;YAAS,SAAS;QAAM;QAC3C,SAAS;YAAC;gBAAE,MAAM;YAAO;SAAE;IAC7B;IACA,WAAW;QACT,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC3C,aAAa;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC5C,WAAW;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC1C,WAAW;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC5C;IACA,OAAO;QACL,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,SAAS;YAAE,MAAM;YAAQ,UAAU;QAAK;QACxC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;AACF,GAAG;IACD,YAAY;AACd;AAEA,+CAA+C;AAC/C,aAAa,KAAK,CAAC;IAAE,MAAM;IAAQ,UAAU;IAAQ,SAAS;AAAO;AACrE,aAAa,KAAK,CAAC;IAAE,qBAAqB;IAAG,sBAAsB;AAAE;AACrE,aAAa,KAAK,CAAC;IAAE,yBAAyB;AAAE;AAChD,aAAa,KAAK,CAAC;IAAE,4BAA4B;AAAE;uCAEpC,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/hostelmeet/src/app/api/hostels/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Hostel from '@/models/Hostel';\n\nexport async function GET(request: NextRequest) {\n  try {\n    await connectDB();\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '12');\n    const search = searchParams.get('search') || '';\n    const seater = searchParams.get('seater') || '';\n    const ac = searchParams.get('ac') || '';\n    const minPrice = searchParams.get('minPrice');\n    const maxPrice = searchParams.get('maxPrice');\n    const amenities = searchParams.get('amenities')?.split(',') || [];\n    const sortBy = searchParams.get('sortBy') || 'name';\n    const sortOrder = searchParams.get('sortOrder') || 'asc';\n\n    // Build query\n    const query: any = {};\n\n    // Search by name, headline, or address\n    if (search) {\n      query.$or = [\n        { name: { $regex: search, $options: 'i' } },\n        { headline: { $regex: search, $options: 'i' } },\n        { address: { $regex: search, $options: 'i' } },\n        { nearestCollege: { $regex: search, $options: 'i' } }\n      ];\n    }\n\n    // Filter by amenities\n    if (amenities.length > 0) {\n      query.amenities = { $in: amenities };\n    }\n\n    // Price filtering (complex due to nested structure)\n    if (minPrice || maxPrice) {\n      const priceConditions: any[] = [];\n      const seaterTypes = ['fourSeater', 'threeSeater', 'twoSeater', 'oneSeater'];\n      const acTypes = ['ac', 'nonAc'];\n\n      seaterTypes.forEach(seaterType => {\n        acTypes.forEach(acType => {\n          const condition: any = {};\n          if (minPrice) condition[`pricing.${seaterType}.${acType}`] = { $gte: parseInt(minPrice) };\n          if (maxPrice) {\n            if (condition[`pricing.${seaterType}.${acType}`]) {\n              condition[`pricing.${seaterType}.${acType}`].$lte = parseInt(maxPrice);\n            } else {\n              condition[`pricing.${seaterType}.${acType}`] = { $lte: parseInt(maxPrice) };\n            }\n          }\n          priceConditions.push(condition);\n        });\n      });\n\n      if (priceConditions.length > 0) {\n        query.$or = query.$or ? [...query.$or, ...priceConditions] : priceConditions;\n      }\n    }\n\n    // Build sort object\n    const sort: any = {};\n    if (sortBy === 'price') {\n      // Sort by minimum price\n      sort['pricing.fourSeater.nonAc'] = sortOrder === 'asc' ? 1 : -1;\n    } else {\n      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;\n    }\n\n    const skip = (page - 1) * limit;\n\n    const [hostels, total] = await Promise.all([\n      Hostel.find(query)\n        .sort(sort)\n        .skip(skip)\n        .limit(limit)\n        .lean(),\n      Hostel.countDocuments(query)\n    ]);\n\n    return NextResponse.json({\n      hostels,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    });\n\n  } catch (error) {\n    console.error('Error fetching hostels:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch hostels' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    await connectDB();\n\n    const body = await request.json();\n    const hostel = new Hostel(body);\n    await hostel.save();\n\n    return NextResponse.json(hostel, { status: 201 });\n\n  } catch (error) {\n    console.error('Error creating hostel:', error);\n    return NextResponse.json(\n      { error: 'Failed to create hostel' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,KAAK,aAAa,GAAG,CAAC,SAAS;QACrC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC,cAAc,MAAM,QAAQ,EAAE;QACjE,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QAEnD,cAAc;QACd,MAAM,QAAa,CAAC;QAEpB,uCAAuC;QACvC,IAAI,QAAQ;YACV,MAAM,GAAG,GAAG;gBACV;oBAAE,MAAM;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC1C;oBAAE,UAAU;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC9C;oBAAE,SAAS;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC7C;oBAAE,gBAAgB;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;aACrD;QACH;QAEA,sBAAsB;QACtB,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,MAAM,SAAS,GAAG;gBAAE,KAAK;YAAU;QACrC;QAEA,oDAAoD;QACpD,IAAI,YAAY,UAAU;YACxB,MAAM,kBAAyB,EAAE;YACjC,MAAM,cAAc;gBAAC;gBAAc;gBAAe;gBAAa;aAAY;YAC3E,MAAM,UAAU;gBAAC;gBAAM;aAAQ;YAE/B,YAAY,OAAO,CAAC,CAAA;gBAClB,QAAQ,OAAO,CAAC,CAAA;oBACd,MAAM,YAAiB,CAAC;oBACxB,IAAI,UAAU,SAAS,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,GAAG;wBAAE,MAAM,SAAS;oBAAU;oBACxF,IAAI,UAAU;wBACZ,IAAI,SAAS,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,EAAE;4BAChD,SAAS,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,GAAG,SAAS;wBAC/D,OAAO;4BACL,SAAS,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,GAAG;gCAAE,MAAM,SAAS;4BAAU;wBAC5E;oBACF;oBACA,gBAAgB,IAAI,CAAC;gBACvB;YACF;YAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG;uBAAI,MAAM,GAAG;uBAAK;iBAAgB,GAAG;YAC/D;QACF;QAEA,oBAAoB;QACpB,MAAM,OAAY,CAAC;QACnB,IAAI,WAAW,SAAS;YACtB,wBAAwB;YACxB,IAAI,CAAC,2BAA2B,GAAG,cAAc,QAAQ,IAAI,CAAC;QAChE,OAAO;YACL,IAAI,CAAC,OAAO,GAAG,cAAc,QAAQ,IAAI,CAAC;QAC5C;QAEA,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,MAAM,CAAC,SAAS,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACzC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,OACT,IAAI,CAAC,MACL,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;YACP,yHAAA,CAAA,UAAM,CAAC,cAAc,CAAC;SACvB;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;QAC1B,MAAM,OAAO,IAAI;QAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;YAAE,QAAQ;QAAI;IAEjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}