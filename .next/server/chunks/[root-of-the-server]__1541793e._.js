module.exports = {

"[project]/.next-internal/server/app/api/seed/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
let cached = global.mongoose || {
    conn: null,
    promise: null
};
if (!global.mongoose) {
    global.mongoose = cached;
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts);
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Hostel.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const HostelSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: true
    },
    headline: {
        type: String,
        required: true
    },
    address: {
        type: String,
        required: true
    },
    location: {
        latitude: {
            type: Number,
            required: true
        },
        longitude: {
            type: Number,
            required: true
        }
    },
    nearbyLandmarks: [
        {
            type: String
        }
    ],
    nearestCollege: {
        type: String,
        required: true
    },
    pricing: {
        fourSeater: {
            ac: {
                type: Number,
                required: true
            },
            nonAc: {
                type: Number,
                required: true
            }
        },
        threeSeater: {
            ac: {
                type: Number,
                required: true
            },
            nonAc: {
                type: Number,
                required: true
            }
        },
        twoSeater: {
            ac: {
                type: Number,
                required: true
            },
            nonAc: {
                type: Number,
                required: true
            }
        },
        oneSeater: {
            ac: {
                type: Number,
                required: true
            },
            nonAc: {
                type: Number,
                required: true
            }
        }
    },
    amenities: [
        {
            type: String
        }
    ],
    travelFacility: [
        {
            type: String
        }
    ],
    foodMenu: {
        breakfast: [
            {
                type: String
            }
        ],
        lunch: [
            {
                type: String
            }
        ],
        dinner: [
            {
                type: String
            }
        ]
    },
    lunchProvidedInCollege: {
        type: Boolean,
        default: false
    },
    cctv: {
        type: Boolean,
        default: false
    },
    securityGuard: {
        type: Boolean,
        default: false
    },
    timeRestrictions: {
        hasRestrictions: {
            type: Boolean,
            default: false
        },
        details: {
            type: String,
            default: ''
        }
    },
    careTaker: {
        name: {
            type: String,
            required: true
        },
        contact: {
            type: String,
            required: true
        }
    },
    photos: {
        fourSeater: {
            ac: [
                {
                    type: String
                }
            ],
            nonAc: [
                {
                    type: String
                }
            ]
        },
        threeSeater: {
            ac: [
                {
                    type: String
                }
            ],
            nonAc: [
                {
                    type: String
                }
            ]
        },
        twoSeater: {
            ac: [
                {
                    type: String
                }
            ],
            nonAc: [
                {
                    type: String
                }
            ]
        },
        oneSeater: {
            ac: [
                {
                    type: String
                }
            ],
            nonAc: [
                {
                    type: String
                }
            ]
        },
        common: [
            {
                type: String
            }
        ]
    },
    gym: {
        type: Boolean,
        default: false
    },
    indoorSports: [
        {
            type: String
        }
    ],
    festsAndEvents: {
        hasEvents: {
            type: Boolean,
            default: false
        },
        details: [
            {
                type: String
            }
        ]
    },
    roomSizes: {
        fourSeater: {
            type: String,
            required: true
        },
        threeSeater: {
            type: String,
            required: true
        },
        twoSeater: {
            type: String,
            required: true
        },
        oneSeater: {
            type: String,
            required: true
        }
    },
    owner: {
        name: {
            type: String,
            required: true
        },
        contact: {
            type: String,
            required: true
        },
        email: {
            type: String,
            required: true
        }
    }
}, {
    timestamps: true
});
// Create indexes for better search performance
HostelSchema.index({
    name: 'text',
    headline: 'text',
    address: 'text'
});
HostelSchema.index({
    'location.latitude': 1,
    'location.longitude': 1
});
HostelSchema.index({
    'pricing.fourSeater.ac': 1
});
HostelSchema.index({
    'pricing.fourSeater.nonAc': 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Hostel || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Hostel', HostelSchema);
}}),
"[project]/src/app/api/seed/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Hostel.ts [app-route] (ecmascript)");
;
;
;
const sampleHostels = [
    {
        name: "Green Valley Hostel",
        headline: "Comfortable stay with modern amenities near IIT Delhi",
        address: "Sector 15, Dwarka, New Delhi, 110075",
        location: {
            latitude: 28.5921,
            longitude: 77.0460
        },
        nearbyLandmarks: [
            "Metro Station",
            "Shopping Mall",
            "Hospital",
            "Bank"
        ],
        nearestCollege: "IIT Delhi",
        pricing: {
            fourSeater: {
                ac: 8000,
                nonAc: 6000
            },
            threeSeater: {
                ac: 10000,
                nonAc: 8000
            },
            twoSeater: {
                ac: 12000,
                nonAc: 10000
            },
            oneSeater: {
                ac: 15000,
                nonAc: 12000
            }
        },
        amenities: [
            "WiFi",
            "Laundry",
            "Mess",
            "Study Room",
            "Recreation Room",
            "Parking"
        ],
        travelFacility: [
            "Bus Service",
            "Metro Connectivity",
            "Auto Stand"
        ],
        foodMenu: {
            breakfast: [
                "Poha",
                "Upma",
                "Bread Butter",
                "Tea/Coffee"
            ],
            lunch: [
                "Dal Rice",
                "Roti",
                "Sabzi",
                "Salad"
            ],
            dinner: [
                "Rice",
                "Dal",
                "Roti",
                "Sabzi",
                "Sweet"
            ]
        },
        lunchProvidedInCollege: true,
        cctv: true,
        securityGuard: true,
        timeRestrictions: {
            hasRestrictions: true,
            details: "Entry allowed till 11 PM"
        },
        careTaker: {
            name: "Rajesh Kumar",
            contact: "+91-9876543210"
        },
        photos: {
            fourSeater: {
                ac: [
                    "/images/green-valley/4s-ac-1.jpg",
                    "/images/green-valley/4s-ac-2.jpg"
                ],
                nonAc: [
                    "/images/green-valley/4s-nonac-1.jpg",
                    "/images/green-valley/4s-nonac-2.jpg"
                ]
            },
            threeSeater: {
                ac: [
                    "/images/green-valley/3s-ac-1.jpg"
                ],
                nonAc: [
                    "/images/green-valley/3s-nonac-1.jpg"
                ]
            },
            twoSeater: {
                ac: [
                    "/images/green-valley/2s-ac-1.jpg"
                ],
                nonAc: [
                    "/images/green-valley/2s-nonac-1.jpg"
                ]
            },
            oneSeater: {
                ac: [
                    "/images/green-valley/1s-ac-1.jpg"
                ],
                nonAc: [
                    "/images/green-valley/1s-nonac-1.jpg"
                ]
            },
            common: [
                "/images/green-valley/common-1.jpg",
                "/images/green-valley/common-2.jpg"
            ]
        },
        gym: true,
        indoorSports: [
            "Table Tennis",
            "Carrom",
            "Chess"
        ],
        festsAndEvents: {
            hasEvents: true,
            details: [
                "Annual Day",
                "Sports Day",
                "Cultural Events"
            ]
        },
        roomSizes: {
            fourSeater: "12x10 feet",
            threeSeater: "12x12 feet",
            twoSeater: "10x12 feet",
            oneSeater: "8x10 feet"
        },
        owner: {
            name: "Suresh Sharma",
            contact: "+91-9876543211",
            email: "<EMAIL>"
        }
    },
    {
        name: "Blue Sky Residency",
        headline: "Premium hostel with excellent facilities near DU North Campus",
        address: "Kamla Nagar, Delhi, 110007",
        location: {
            latitude: 28.6850,
            longitude: 77.2090
        },
        nearbyLandmarks: [
            "Metro Station",
            "Market",
            "Library",
            "Cafe"
        ],
        nearestCollege: "Delhi University North Campus",
        pricing: {
            fourSeater: {
                ac: 9000,
                nonAc: 7000
            },
            threeSeater: {
                ac: 11000,
                nonAc: 9000
            },
            twoSeater: {
                ac: 13000,
                nonAc: 11000
            },
            oneSeater: {
                ac: 16000,
                nonAc: 13000
            }
        },
        amenities: [
            "WiFi",
            "Laundry",
            "Mess",
            "Study Room",
            "Library",
            "Parking",
            "Generator"
        ],
        travelFacility: [
            "Bus Service",
            "Metro Connectivity",
            "Cycle Stand"
        ],
        foodMenu: {
            breakfast: [
                "Aloo Paratha",
                "Chole",
                "Tea/Coffee",
                "Fruits"
            ],
            lunch: [
                "Rajma Rice",
                "Roti",
                "Sabzi",
                "Curd"
            ],
            dinner: [
                "Biryani",
                "Dal",
                "Roti",
                "Raita",
                "Ice Cream"
            ]
        },
        lunchProvidedInCollege: false,
        cctv: true,
        securityGuard: true,
        timeRestrictions: {
            hasRestrictions: true,
            details: "Entry allowed till 10:30 PM"
        },
        careTaker: {
            name: "Priya Singh",
            contact: "+91-9876543212"
        },
        photos: {
            fourSeater: {
                ac: [
                    "/images/blue-sky/4s-ac-1.jpg"
                ],
                nonAc: [
                    "/images/blue-sky/4s-nonac-1.jpg"
                ]
            },
            threeSeater: {
                ac: [
                    "/images/blue-sky/3s-ac-1.jpg"
                ],
                nonAc: [
                    "/images/blue-sky/3s-nonac-1.jpg"
                ]
            },
            twoSeater: {
                ac: [
                    "/images/blue-sky/2s-ac-1.jpg"
                ],
                nonAc: [
                    "/images/blue-sky/2s-nonac-1.jpg"
                ]
            },
            oneSeater: {
                ac: [
                    "/images/blue-sky/1s-ac-1.jpg"
                ],
                nonAc: [
                    "/images/blue-sky/1s-nonac-1.jpg"
                ]
            },
            common: [
                "/images/blue-sky/common-1.jpg"
            ]
        },
        gym: false,
        indoorSports: [
            "Badminton",
            "Table Tennis"
        ],
        festsAndEvents: {
            hasEvents: true,
            details: [
                "Diwali Celebration",
                "Holi Festival",
                "New Year Party"
            ]
        },
        roomSizes: {
            fourSeater: "14x12 feet",
            threeSeater: "12x14 feet",
            twoSeater: "10x14 feet",
            oneSeater: "8x12 feet"
        },
        owner: {
            name: "Amit Gupta",
            contact: "+91-9876543213",
            email: "<EMAIL>"
        }
    }
];
async function POST() {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        // Clear existing data
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].deleteMany({});
        // Insert sample data
        const hostels = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].insertMany(sampleHostels);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'Database seeded successfully',
            count: hostels.length
        });
    } catch (error) {
        console.error('Seeding error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to seed database'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1541793e._.js.map