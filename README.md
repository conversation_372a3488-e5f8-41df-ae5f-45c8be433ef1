# HostelMeet - Hostel Listing Platform

A comprehensive hostel listing platform built with Next.js, TypeScript, and MongoDB Atlas. Find and compare hostels with detailed information, amenities, pricing, and location-based services.

## 🚀 Features

### For Users
- **Browse Hostels**: View hostels with detailed information including pricing, amenities, and photos
- **Advanced Filtering**: Filter by room type, AC/Non-AC, price range, amenities, and more
- **Search Functionality**: Search by hostel name, location, or college
- **Grid/List View**: Toggle between grid and list view for hostel listings
- **Detailed Hostel Pages**: Comprehensive information including:
  - Photo galleries organized by room type and AC preference
  - Pricing for all room configurations
  - Amenities and facilities
  - Food menu and meal information
  - Contact details and location
  - Distance from user's current location
- **Comparison Tool**: Compare up to 4 hostels side-by-side
- **Responsive Design**: Fully responsive across all devices

### For Administrators
- **Admin Dashboard**: Secure admin panel with authentication
- **Hostel Management**: Add, edit, and delete hostel listings
- **Real-time Statistics**: View hostel counts and feature statistics
- **Sample Data Seeding**: Populate database with sample data for testing

## 🛠 Tech Stack

- **Frontend**: Next.js 15, React, TypeScript
- **Styling**: Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: MongoDB Atlas with Mongoose ODM
- **Authentication**: JWT-based admin authentication
- **Icons**: Lucide React
- **Location Services**: Browser Geolocation API

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hostelmeet
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env.local` file in the root directory:
   ```env
   # MongoDB Atlas Connection
   MONGODB_URI=mongodb+srv://your-username:<EMAIL>/hostelmeet?retryWrites=true&w=majority

   # NextAuth Configuration
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-nextauth-secret-key-here

   # Admin Credentials
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=nk10nikhil

   # JWT Secret
   JWT_SECRET=your-jwt-secret-key-here

   # Google Maps API (optional)
   NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key-here
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🗄 Database Setup

### MongoDB Atlas Setup
1. Create a MongoDB Atlas account at https://www.mongodb.com/atlas
2. Create a new cluster
3. Create a database user with read/write permissions
4. Get your connection string and update the `MONGODB_URI` in `.env.local`

### Sample Data
The application includes mock data that works without database connection. To seed the database with sample data:
1. Ensure MongoDB connection is working
2. Navigate to `/admin/login` and login with admin credentials
3. Click "Seed Sample Data" in the admin dashboard

## 🔐 Admin Access

**Default Admin Credentials:**
- Email: `<EMAIL>`
- Password: `nk10nikhil`

Access the admin dashboard at `/admin/login`

## 📱 Key Pages

- **Home** (`/`): Landing page with features and call-to-action
- **Hostels** (`/hostels`): Main hostel listing page with filters
- **Hostel Detail** (`/hostels/[id]`): Individual hostel information
- **Comparison** (`/comparison`): Compare multiple hostels
- **Admin Login** (`/admin/login`): Admin authentication
- **Admin Dashboard** (`/admin/dashboard`): Hostel management

## 🎨 Design Features

- **Modern UI**: Clean, professional design with intuitive navigation
- **Responsive Layout**: Optimized for desktop, tablet, and mobile
- **Interactive Elements**: Hover effects, smooth transitions, and loading states
- **Accessibility**: Semantic HTML and keyboard navigation support
- **Performance**: Optimized images and efficient data loading

## 🔧 Development

### Project Structure
```
src/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── admin/             # Admin pages
│   ├── hostels/           # Hostel pages
│   └── comparison/        # Comparison page
├── components/            # Reusable components
│   ├── layout/           # Layout components
│   └── hostel/           # Hostel-specific components
├── lib/                  # Utility functions
├── models/               # Database models
└── styles/               # Global styles
```

### Key Components
- **HostelCard**: Displays hostel information in grid/list format
- **HostelFilters**: Advanced filtering interface
- **Navbar**: Main navigation component
- **Admin Dashboard**: Hostel management interface

### API Endpoints
- `GET /api/hostels` - List hostels with filtering
- `GET /api/hostels/[id]` - Get individual hostel
- `POST /api/hostels` - Create new hostel (admin)
- `PUT /api/hostels/[id]` - Update hostel (admin)
- `DELETE /api/hostels/[id]` - Delete hostel (admin)
- `POST /api/auth/login` - Admin authentication
- `POST /api/seed` - Seed sample data

## 🚀 Deployment

The application is ready for deployment on platforms like Vercel, Netlify, or any Node.js hosting service.

### Vercel Deployment
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support or questions, please contact: <EMAIL>
