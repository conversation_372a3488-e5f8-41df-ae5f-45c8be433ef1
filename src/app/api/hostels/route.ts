import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Hostel from '@/models/Hostel';
import { mockHostels } from '@/lib/mockData';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const search = searchParams.get('search') || '';

    // Try to connect to MongoDB, fallback to mock data if it fails
    let hostels;
    let total;

    try {
      await connectDB();

      const seater = searchParams.get('seater') || '';
      const ac = searchParams.get('ac') || '';
      const minPrice = searchParams.get('minPrice');
      const maxPrice = searchParams.get('maxPrice');
      const amenities = searchParams.get('amenities')?.split(',') || [];
      const sortBy = searchParams.get('sortBy') || 'name';
      const sortOrder = searchParams.get('sortOrder') || 'asc';

      // Build query
      const query: any = {};

      // Search by name, headline, or address
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { headline: { $regex: search, $options: 'i' } },
          { address: { $regex: search, $options: 'i' } },
          { nearestCollege: { $regex: search, $options: 'i' } }
        ];
      }

      // Filter by amenities
      if (amenities.length > 0) {
        query.amenities = { $in: amenities };
      }

      // Price filtering (complex due to nested structure)
      if (minPrice || maxPrice) {
        const priceConditions: any[] = [];
        const seaterTypes = ['fourSeater', 'threeSeater', 'twoSeater', 'oneSeater'];
        const acTypes = ['ac', 'nonAc'];

        seaterTypes.forEach(seaterType => {
          acTypes.forEach(acType => {
            const condition: any = {};
            if (minPrice) condition[`pricing.${seaterType}.${acType}`] = { $gte: parseInt(minPrice) };
            if (maxPrice) {
              if (condition[`pricing.${seaterType}.${acType}`]) {
                condition[`pricing.${seaterType}.${acType}`].$lte = parseInt(maxPrice);
              } else {
                condition[`pricing.${seaterType}.${acType}`] = { $lte: parseInt(maxPrice) };
              }
            }
            priceConditions.push(condition);
          });
        });

        if (priceConditions.length > 0) {
          query.$or = query.$or ? [...query.$or, ...priceConditions] : priceConditions;
        }
      }

      // Build sort object
      const sort: any = {};
      if (sortBy === 'price') {
        // Sort by minimum price
        sort['pricing.fourSeater.nonAc'] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      }

      const skip = (page - 1) * limit;

      const [dbHostels, dbTotal] = await Promise.all([
        Hostel.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        Hostel.countDocuments(query)
      ]);

      hostels = dbHostels;
      total = dbTotal;

    } catch (dbError) {
      console.log('MongoDB connection failed, using mock data');

      // Use mock data with filtering
      let filteredHostels = [...mockHostels];

      // Apply search filter
      if (search) {
        filteredHostels = filteredHostels.filter(hostel =>
          hostel.name.toLowerCase().includes(search.toLowerCase()) ||
          hostel.headline.toLowerCase().includes(search.toLowerCase()) ||
          hostel.address.toLowerCase().includes(search.toLowerCase()) ||
          hostel.nearestCollege.toLowerCase().includes(search.toLowerCase())
        );
      }

      total = filteredHostels.length;
      const skip = (page - 1) * limit;
      hostels = filteredHostels.slice(skip, skip + limit);
    }

    return NextResponse.json({
      hostels,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching hostels:', error);
    return NextResponse.json(
      { error: 'Failed to fetch hostels' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const hostel = new Hostel(body);
    await hostel.save();

    return NextResponse.json(hostel, { status: 201 });

  } catch (error) {
    console.error('Error creating hostel:', error);
    return NextResponse.json(
      { error: 'Failed to create hostel' },
      { status: 500 }
    );
  }
}
