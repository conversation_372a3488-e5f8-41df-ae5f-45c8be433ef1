import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Hostel from '@/models/Hostel';
import { mockHostels } from '@/lib/mockData';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    let hostel;

    try {
      await connectDB();
      hostel = await Hostel.findById(params.id).lean();
    } catch (dbError) {
      console.log('MongoDB connection failed, using mock data');
      hostel = mockHostels.find(h => h._id === params.id);
    }

    if (!hostel) {
      return NextResponse.json(
        { error: 'Hostel not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(hostel);

  } catch (error) {
    console.error('Error fetching hostel:', error);
    return NextResponse.json(
      { error: 'Failed to fetch hostel' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();

    const body = await request.json();
    const hostel = await Hostel.findByIdAndUpdate(
      params.id,
      body,
      { new: true, runValidators: true }
    );

    if (!hostel) {
      return NextResponse.json(
        { error: 'Hostel not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(hostel);

  } catch (error) {
    console.error('Error updating hostel:', error);
    return NextResponse.json(
      { error: 'Failed to update hostel' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();

    const hostel = await Hostel.findByIdAndDelete(params.id);

    if (!hostel) {
      return NextResponse.json(
        { error: 'Hostel not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Hostel deleted successfully' });

  } catch (error) {
    console.error('Error deleting hostel:', error);
    return NextResponse.json(
      { error: 'Failed to delete hostel' },
      { status: 500 }
    );
  }
}
