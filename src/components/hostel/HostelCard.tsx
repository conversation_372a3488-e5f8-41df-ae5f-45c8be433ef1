'use client';

import Link from 'next/link';
import Image from 'next/image';
import { MapPin, Star, Wifi, Car, Users, Shield, Clock, Utensils } from 'lucide-react';
import { IHostel } from '@/models/Hostel';
import { formatPrice, getMinPrice, getMaxPrice, getPriceForSeaterAndAC } from '@/lib/utils';
import PlaceholderImage from '@/components/ui/PlaceholderImage';

interface HostelCardProps {
  hostel: IHostel;
  viewMode: 'grid' | 'list';
  filters: {
    seater: string;
    ac: string;
    minPrice: string;
    maxPrice: string;
    amenities: string[];
    sortBy: string;
    sortOrder: string;
  };
}

export default function HostelCard({ hostel, viewMode, filters }: HostelCardProps) {
  const getDisplayPrice = () => {
    if (filters.seater && filters.ac) {
      return getPriceForSeaterAndAC(hostel.pricing, filters.seater, filters.ac);
    }
    return getMinPrice(hostel.pricing);
  };

  const getPriceRange = () => {
    if (filters.seater && filters.ac) {
      const price = getPriceForSeaterAndAC(hostel.pricing, filters.seater, filters.ac);
      return formatPrice(price);
    }
    const min = getMinPrice(hostel.pricing);
    const max = getMaxPrice(hostel.pricing);
    return min === max ? formatPrice(min) : `${formatPrice(min)} - ${formatPrice(max)}`;
  };

  const getMainImage = () => {
    if (filters.seater && filters.ac) {
      const seaterKey = filters.seater as keyof typeof hostel.photos;
      const acKey = filters.ac as keyof typeof hostel.photos[typeof seaterKey];
      const images = hostel.photos[seaterKey][acKey];
      if (images && images.length > 0) {
        return images[0];
      }
    }

    // Fallback to common images or first available image
    if (hostel.photos.common && hostel.photos.common.length > 0) {
      return hostel.photos.common[0];
    }

    // Find first available image
    for (const seaterType of ['fourSeater', 'threeSeater', 'twoSeater', 'oneSeater'] as const) {
      for (const acType of ['ac', 'nonAc'] as const) {
        const images = hostel.photos[seaterType][acType];
        if (images && images.length > 0) {
          return images[0];
        }
      }
    }

    return '/images/placeholder-hostel.jpg';
  };

  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
        <div className="flex">
          <div className="w-80 h-48 relative flex-shrink-0">
            {getMainImage() !== '/images/placeholder-hostel.jpg' ? (
              <Image
                src={getMainImage()}
                alt={hostel.name}
                fill
                className="object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />
            ) : (
              <PlaceholderImage
                width={320}
                height={192}
                text={hostel.name}
                className="w-full h-full rounded-l-lg"
              />
            )}
          </div>

          <div className="flex-1 p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{hostel.name}</h3>
                <p className="text-gray-600 mb-2">{hostel.headline}</p>
                <div className="flex items-center text-gray-500 text-sm mb-2">
                  <MapPin className="h-4 w-4 mr-1" />
                  {hostel.address}
                </div>
                <div className="text-sm text-blue-600 mb-4">
                  Near {hostel.nearestCollege}
                </div>
              </div>

              <div className="text-right">
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {getPriceRange()}
                </div>
                <div className="text-sm text-gray-500">
                  {filters.seater && filters.ac ? 'per month' : 'starting from'}
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              {hostel.amenities.slice(0, 6).map((amenity, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                >
                  {amenity}
                </span>
              ))}
              {hostel.amenities.length > 6 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                  +{hostel.amenities.length - 6} more
                </span>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-gray-600">
                {hostel.cctv && (
                  <div className="flex items-center gap-1">
                    <Shield className="h-4 w-4" />
                    <span>CCTV</span>
                  </div>
                )}
                {hostel.securityGuard && (
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>Security</span>
                  </div>
                )}
                {hostel.gym && (
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>Gym</span>
                  </div>
                )}
              </div>

              <Link
                href={`/hostels/${hostel._id}`}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                View Details
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <div className="relative h-48">
        {getMainImage() !== '/images/placeholder-hostel.jpg' ? (
          <Image
            src={getMainImage()}
            alt={hostel.name}
            fill
            className="object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
            }}
          />
        ) : (
          <PlaceholderImage
            width={400}
            height={192}
            text={hostel.name}
            className="w-full h-full"
          />
        )}
      </div>

      <div className="p-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{hostel.name}</h3>
          <p className="text-gray-600 text-sm mb-2">{hostel.headline}</p>
          <div className="flex items-center text-gray-500 text-sm mb-2">
            <MapPin className="h-4 w-4 mr-1" />
            <span className="truncate">{hostel.address}</span>
          </div>
          <div className="text-sm text-blue-600">
            Near {hostel.nearestCollege}
          </div>
        </div>

        <div className="flex flex-wrap gap-1 mb-4">
          {hostel.amenities.slice(0, 4).map((amenity, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
            >
              {amenity}
            </span>
          ))}
          {hostel.amenities.length > 4 && (
            <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
              +{hostel.amenities.length - 4}
            </span>
          )}
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3 text-xs text-gray-600">
            {hostel.cctv && <Shield className="h-4 w-4" />}
            {hostel.securityGuard && <Users className="h-4 w-4" />}
            {hostel.gym && <Utensils className="h-4 w-4" />}
          </div>

          <div className="text-right">
            <div className="text-lg font-bold text-green-600">
              {getPriceRange()}
            </div>
            <div className="text-xs text-gray-500">
              {filters.seater && filters.ac ? 'per month' : 'starting from'}
            </div>
          </div>
        </div>

        <Link
          href={`/hostels/${hostel._id}`}
          className="block w-full bg-blue-600 text-white text-center py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          View Details
        </Link>
      </div>
    </div>
  );
}
