'use client';

import { useState } from 'react';
import { X } from 'lucide-react';

interface FiltersProps {
  filters: {
    seater: string;
    ac: string;
    minPrice: string;
    maxPrice: string;
    amenities: string[];
    sortBy: string;
    sortOrder: string;
  };
  onFiltersChange: (filters: any) => void;
}

const availableAmenities = [
  'WiFi', 'Laundry', 'Mess', 'Study Room', 'Recreation Room', 'Parking',
  'Generator', 'Library', 'Gym', 'CCTV', 'Security Guard', 'AC', 'Cooler'
];

export default function HostelFilters({ filters, onFiltersChange }: FiltersProps) {
  const handleFilterChange = (key: string, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const handleAmenityToggle = (amenity: string) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(a => a !== amenity)
      : [...filters.amenities, amenity];
    
    handleFilterChange('amenities', newAmenities);
  };

  const clearFilters = () => {
    onFiltersChange({
      seater: '',
      ac: '',
      minPrice: '',
      maxPrice: '',
      amenities: [],
      sortBy: 'name',
      sortOrder: 'asc'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Filters</h3>
        <button
          onClick={clearFilters}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Clear All
        </button>
      </div>

      {/* Room Type */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Room Type
        </label>
        <select
          value={filters.seater}
          onChange={(e) => handleFilterChange('seater', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Room Types</option>
          <option value="fourSeater">4 Seater</option>
          <option value="threeSeater">3 Seater</option>
          <option value="twoSeater">2 Seater</option>
          <option value="oneSeater">1 Seater</option>
        </select>
      </div>

      {/* AC/Non-AC */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          AC Preference
        </label>
        <select
          value={filters.ac}
          onChange={(e) => handleFilterChange('ac', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Both AC & Non-AC</option>
          <option value="ac">AC Only</option>
          <option value="nonAc">Non-AC Only</option>
        </select>
      </div>

      {/* Price Range */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Price Range (₹)
        </label>
        <div className="grid grid-cols-2 gap-2">
          <input
            type="number"
            placeholder="Min"
            value={filters.minPrice}
            onChange={(e) => handleFilterChange('minPrice', e.target.value)}
            className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <input
            type="number"
            placeholder="Max"
            value={filters.maxPrice}
            onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
            className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Amenities */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Amenities
        </label>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {availableAmenities.map((amenity) => (
            <label key={amenity} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.amenities.includes(amenity)}
                onChange={() => handleAmenityToggle(amenity)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">{amenity}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Sort By */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Sort By
        </label>
        <select
          value={filters.sortBy}
          onChange={(e) => handleFilterChange('sortBy', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="name">Name</option>
          <option value="price">Price</option>
          <option value="createdAt">Newest First</option>
        </select>
      </div>

      {/* Sort Order */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Order
        </label>
        <select
          value={filters.sortOrder}
          onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="asc">Ascending</option>
          <option value="desc">Descending</option>
        </select>
      </div>
    </div>
  );
}
