'use client';

import { Building } from 'lucide-react';

interface PlaceholderImageProps {
  width?: number;
  height?: number;
  text?: string;
  className?: string;
}

export default function PlaceholderImage({ 
  width = 400, 
  height = 300, 
  text = "Hostel Image", 
  className = "" 
}: PlaceholderImageProps) {
  return (
    <div 
      className={`bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center ${className}`}
      style={{ width, height }}
    >
      <div className="text-center text-gray-500">
        <Building className="h-12 w-12 mx-auto mb-2" />
        <p className="text-sm font-medium">{text}</p>
      </div>
    </div>
  );
}
