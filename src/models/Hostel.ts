import mongoose, { Document, Schema } from 'mongoose';

export interface IHostel extends Document {
  name: string;
  headline: string;
  address: string;
  location: {
    latitude: number;
    longitude: number;
  };
  nearbyLandmarks: string[];
  nearestCollege: string;
  pricing: {
    fourSeater: {
      ac: number;
      nonAc: number;
    };
    threeSeater: {
      ac: number;
      nonAc: number;
    };
    twoSeater: {
      ac: number;
      nonAc: number;
    };
    oneSeater: {
      ac: number;
      nonAc: number;
    };
  };
  amenities: string[];
  travelFacility: string[];
  foodMenu: {
    breakfast: string[];
    lunch: string[];
    dinner: string[];
  };
  lunchProvidedInCollege: boolean;
  cctv: boolean;
  securityGuard: boolean;
  timeRestrictions: {
    hasRestrictions: boolean;
    details: string;
  };
  careTaker: {
    name: string;
    contact: string;
  };
  photos: {
    fourSeater: {
      ac: string[];
      nonAc: string[];
    };
    threeSeater: {
      ac: string[];
      nonAc: string[];
    };
    twoSeater: {
      ac: string[];
      nonAc: string[];
    };
    oneSeater: {
      ac: string[];
      nonAc: string[];
    };
    common: string[];
  };
  gym: boolean;
  indoorSports: string[];
  festsAndEvents: {
    hasEvents: boolean;
    details: string[];
  };
  roomSizes: {
    fourSeater: string;
    threeSeater: string;
    twoSeater: string;
    oneSeater: string;
  };
  owner: {
    name: string;
    contact: string;
    email: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const HostelSchema = new Schema<IHostel>({
  name: { type: String, required: true },
  headline: { type: String, required: true },
  address: { type: String, required: true },
  location: {
    latitude: { type: Number, required: true },
    longitude: { type: Number, required: true }
  },
  nearbyLandmarks: [{ type: String }],
  nearestCollege: { type: String, required: true },
  pricing: {
    fourSeater: {
      ac: { type: Number, required: true },
      nonAc: { type: Number, required: true }
    },
    threeSeater: {
      ac: { type: Number, required: true },
      nonAc: { type: Number, required: true }
    },
    twoSeater: {
      ac: { type: Number, required: true },
      nonAc: { type: Number, required: true }
    },
    oneSeater: {
      ac: { type: Number, required: true },
      nonAc: { type: Number, required: true }
    }
  },
  amenities: [{ type: String }],
  travelFacility: [{ type: String }],
  foodMenu: {
    breakfast: [{ type: String }],
    lunch: [{ type: String }],
    dinner: [{ type: String }]
  },
  lunchProvidedInCollege: { type: Boolean, default: false },
  cctv: { type: Boolean, default: false },
  securityGuard: { type: Boolean, default: false },
  timeRestrictions: {
    hasRestrictions: { type: Boolean, default: false },
    details: { type: String, default: '' }
  },
  careTaker: {
    name: { type: String, required: true },
    contact: { type: String, required: true }
  },
  photos: {
    fourSeater: {
      ac: [{ type: String }],
      nonAc: [{ type: String }]
    },
    threeSeater: {
      ac: [{ type: String }],
      nonAc: [{ type: String }]
    },
    twoSeater: {
      ac: [{ type: String }],
      nonAc: [{ type: String }]
    },
    oneSeater: {
      ac: [{ type: String }],
      nonAc: [{ type: String }]
    },
    common: [{ type: String }]
  },
  gym: { type: Boolean, default: false },
  indoorSports: [{ type: String }],
  festsAndEvents: {
    hasEvents: { type: Boolean, default: false },
    details: [{ type: String }]
  },
  roomSizes: {
    fourSeater: { type: String, required: true },
    threeSeater: { type: String, required: true },
    twoSeater: { type: String, required: true },
    oneSeater: { type: String, required: true }
  },
  owner: {
    name: { type: String, required: true },
    contact: { type: String, required: true },
    email: { type: String, required: true }
  }
}, {
  timestamps: true
});

// Create indexes for better search performance
HostelSchema.index({ name: 'text', headline: 'text', address: 'text' });
HostelSchema.index({ 'location.latitude': 1, 'location.longitude': 1 });
HostelSchema.index({ 'pricing.fourSeater.ac': 1 });
HostelSchema.index({ 'pricing.fourSeater.nonAc': 1 });

export default mongoose.models.Hostel || mongoose.model<IHostel>('Hostel', HostelSchema);
