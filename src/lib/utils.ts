import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price)
}

export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c; // Distance in kilometers
  return Math.round(d * 100) / 100; // Round to 2 decimal places
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}

export function getUserLocation(): Promise<{ latitude: number; longitude: number }> {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser.'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        });
      },
      (error) => {
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 600000, // 10 minutes
      }
    );
  });
}

export function getMinPrice(pricing: any): number {
  const prices = [
    pricing.fourSeater.ac,
    pricing.fourSeater.nonAc,
    pricing.threeSeater.ac,
    pricing.threeSeater.nonAc,
    pricing.twoSeater.ac,
    pricing.twoSeater.nonAc,
    pricing.oneSeater.ac,
    pricing.oneSeater.nonAc,
  ];
  return Math.min(...prices);
}

export function getMaxPrice(pricing: any): number {
  const prices = [
    pricing.fourSeater.ac,
    pricing.fourSeater.nonAc,
    pricing.threeSeater.ac,
    pricing.threeSeater.nonAc,
    pricing.twoSeater.ac,
    pricing.twoSeater.nonAc,
    pricing.oneSeater.ac,
    pricing.oneSeater.nonAc,
  ];
  return Math.max(...prices);
}

export function getPriceForSeaterAndAC(pricing: any, seater: string, ac: string): number {
  const seaterKey = seater as keyof typeof pricing;
  const acKey = ac as keyof typeof pricing[typeof seaterKey];
  return pricing[seaterKey][acKey];
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
