import { IHostel } from '@/models/Hostel';

export const mockHostels: IHostel[] = [
  {
    _id: '1',
    name: "Green Valley Hostel",
    headline: "Comfortable stay with modern amenities near IIT Delhi",
    address: "Sector 15, Dwarka, New Delhi, 110075",
    location: {
      latitude: 28.5921,
      longitude: 77.0460
    },
    nearbyLandmarks: ["Metro Station", "Shopping Mall", "Hospital", "Bank"],
    nearestCollege: "IIT Delhi",
    pricing: {
      fourSeater: { ac: 8000, nonAc: 6000 },
      threeSeater: { ac: 10000, nonAc: 8000 },
      twoSeater: { ac: 12000, nonAc: 10000 },
      oneSeater: { ac: 15000, nonAc: 12000 }
    },
    amenities: ["WiFi", "Laundry", "Mess", "Study Room", "Recreation Room", "Parking"],
    travelFacility: ["Bus Service", "Metro Connectivity", "Auto Stand"],
    foodMenu: {
      breakfast: ["<PERSON><PERSON>", "<PERSON><PERSON>", "Bread Butter", "Tea/Coffee"],
      lunch: ["<PERSON> Rice", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ad"],
      dinner: ["<PERSON>", "<PERSON>", "<PERSON>ot<PERSON>", "<PERSON><PERSON><PERSON>", "Sweet"]
    },
    lunchProvidedInCollege: true,
    cctv: true,
    securityGuard: true,
    timeRestrictions: {
      hasRestrictions: true,
      details: "Entry allowed till 11 PM"
    },
    careTaker: {
      name: "Rajesh Kumar",
      contact: "+91-**********"
    },
    photos: {
      fourSeater: {
        ac: ["/images/green-valley/4s-ac-1.jpg", "/images/green-valley/4s-ac-2.jpg"],
        nonAc: ["/images/green-valley/4s-nonac-1.jpg", "/images/green-valley/4s-nonac-2.jpg"]
      },
      threeSeater: {
        ac: ["/images/green-valley/3s-ac-1.jpg"],
        nonAc: ["/images/green-valley/3s-nonac-1.jpg"]
      },
      twoSeater: {
        ac: ["/images/green-valley/2s-ac-1.jpg"],
        nonAc: ["/images/green-valley/2s-nonac-1.jpg"]
      },
      oneSeater: {
        ac: ["/images/green-valley/1s-ac-1.jpg"],
        nonAc: ["/images/green-valley/1s-nonac-1.jpg"]
      },
      common: ["/images/green-valley/common-1.jpg", "/images/green-valley/common-2.jpg"]
    },
    gym: true,
    indoorSports: ["Table Tennis", "Carrom", "Chess"],
    festsAndEvents: {
      hasEvents: true,
      details: ["Annual Day", "Sports Day", "Cultural Events"]
    },
    roomSizes: {
      fourSeater: "12x10 feet",
      threeSeater: "12x12 feet",
      twoSeater: "10x12 feet",
      oneSeater: "8x10 feet"
    },
    owner: {
      name: "Suresh Sharma",
      contact: "+91-9876543211",
      email: "<EMAIL>"
    },
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    _id: '2',
    name: "Blue Sky Residency",
    headline: "Premium hostel with excellent facilities near DU North Campus",
    address: "Kamla Nagar, Delhi, 110007",
    location: {
      latitude: 28.6850,
      longitude: 77.2090
    },
    nearbyLandmarks: ["Metro Station", "Market", "Library", "Cafe"],
    nearestCollege: "Delhi University North Campus",
    pricing: {
      fourSeater: { ac: 9000, nonAc: 7000 },
      threeSeater: { ac: 11000, nonAc: 9000 },
      twoSeater: { ac: 13000, nonAc: 11000 },
      oneSeater: { ac: 16000, nonAc: 13000 }
    },
    amenities: ["WiFi", "Laundry", "Mess", "Study Room", "Library", "Parking", "Generator"],
    travelFacility: ["Bus Service", "Metro Connectivity", "Cycle Stand"],
    foodMenu: {
      breakfast: ["Aloo Paratha", "Chole", "Tea/Coffee", "Fruits"],
      lunch: ["Rajma Rice", "Roti", "Sabzi", "Curd"],
      dinner: ["Biryani", "Dal", "Roti", "Raita", "Ice Cream"]
    },
    lunchProvidedInCollege: false,
    cctv: true,
    securityGuard: true,
    timeRestrictions: {
      hasRestrictions: true,
      details: "Entry allowed till 10:30 PM"
    },
    careTaker: {
      name: "Priya Singh",
      contact: "+91-9876543212"
    },
    photos: {
      fourSeater: {
        ac: ["/images/blue-sky/4s-ac-1.jpg"],
        nonAc: ["/images/blue-sky/4s-nonac-1.jpg"]
      },
      threeSeater: {
        ac: ["/images/blue-sky/3s-ac-1.jpg"],
        nonAc: ["/images/blue-sky/3s-nonac-1.jpg"]
      },
      twoSeater: {
        ac: ["/images/blue-sky/2s-ac-1.jpg"],
        nonAc: ["/images/blue-sky/2s-nonac-1.jpg"]
      },
      oneSeater: {
        ac: ["/images/blue-sky/1s-ac-1.jpg"],
        nonAc: ["/images/blue-sky/1s-nonac-1.jpg"]
      },
      common: ["/images/blue-sky/common-1.jpg"]
    },
    gym: false,
    indoorSports: ["Badminton", "Table Tennis"],
    festsAndEvents: {
      hasEvents: true,
      details: ["Diwali Celebration", "Holi Festival", "New Year Party"]
    },
    roomSizes: {
      fourSeater: "14x12 feet",
      threeSeater: "12x14 feet",
      twoSeater: "10x14 feet",
      oneSeater: "8x12 feet"
    },
    owner: {
      name: "Amit Gupta",
      contact: "+91-9876543213",
      email: "<EMAIL>"
    },
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20')
  },
  {
    _id: '3',
    name: "Sunrise Hostel",
    headline: "Budget-friendly accommodation with all basic amenities",
    address: "Lajpat Nagar, New Delhi, 110024",
    location: {
      latitude: 28.5665,
      longitude: 77.2431
    },
    nearbyLandmarks: ["Metro Station", "Market", "Hospital"],
    nearestCollege: "Jamia Millia Islamia",
    pricing: {
      fourSeater: { ac: 7000, nonAc: 5000 },
      threeSeater: { ac: 8500, nonAc: 6500 },
      twoSeater: { ac: 10000, nonAc: 8000 },
      oneSeater: { ac: 12000, nonAc: 9500 }
    },
    amenities: ["WiFi", "Laundry", "Mess", "Study Room", "Parking"],
    travelFacility: ["Bus Service", "Metro Connectivity"],
    foodMenu: {
      breakfast: ["Paratha", "Tea", "Fruits"],
      lunch: ["Dal Rice", "Roti", "Sabzi"],
      dinner: ["Rice", "Dal", "Roti", "Sabzi"]
    },
    lunchProvidedInCollege: false,
    cctv: true,
    securityGuard: false,
    timeRestrictions: {
      hasRestrictions: true,
      details: "Entry allowed till 11:30 PM"
    },
    careTaker: {
      name: "Mohan Lal",
      contact: "+91-9876543214"
    },
    photos: {
      fourSeater: {
        ac: ["/images/sunrise/4s-ac-1.jpg"],
        nonAc: ["/images/sunrise/4s-nonac-1.jpg"]
      },
      threeSeater: {
        ac: ["/images/sunrise/3s-ac-1.jpg"],
        nonAc: ["/images/sunrise/3s-nonac-1.jpg"]
      },
      twoSeater: {
        ac: ["/images/sunrise/2s-ac-1.jpg"],
        nonAc: ["/images/sunrise/2s-nonac-1.jpg"]
      },
      oneSeater: {
        ac: ["/images/sunrise/1s-ac-1.jpg"],
        nonAc: ["/images/sunrise/1s-nonac-1.jpg"]
      },
      common: ["/images/sunrise/common-1.jpg"]
    },
    gym: false,
    indoorSports: ["Carrom"],
    festsAndEvents: {
      hasEvents: false,
      details: []
    },
    roomSizes: {
      fourSeater: "10x10 feet",
      threeSeater: "10x12 feet",
      twoSeater: "8x10 feet",
      oneSeater: "8x8 feet"
    },
    owner: {
      name: "Ramesh Gupta",
      contact: "+91-9876543215",
      email: "<EMAIL>"
    },
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-01-25')
  }
] as IHostel[];
